{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?2568", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?a336", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?7037", "uni-app:///pages/shifuIndex.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?376e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?96cd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "orderData", "showDing<PERSON><PERSON>", "showCate", "infodata", "tmplIds", "status", "id", "shifuId", "currentTab", "tabsList", "name", "badge", "highValueSubMenu", "type", "showHighValueMenu", "listType", "list", "confirmshow", "masterModalShow", "detailModalShow", "content", "input", "area_id", "limit", "page", "bannerList", "configInfo", "getconfigs", "list1", "text1", "lng", "shi<PERSON><PERSON><PERSON>us", "msg", "QuotationCounts", "lat", "cateList", "currentCateId", "currentCateName", "copyCateList", "province", "city", "district", "isPageLoaded", "selectedItem", "priceRange", "min", "max", "customMin", "customMax", "distance", "appliedDistance", "isPriceCollapsed", "isDistanceCollapsed", "isCategoryCollapsed", "showFilter", "showFilterPanel", "currentActivityType", "areaCount", "computed", "configInfos", "regeocode", "refreshReceiving", "priceFilterText", "isPriceFilterActive", "isDistanceFilterActive", "isCategoryFilterActive", "methods", "getCurrentPlatform", "textclick", "switchTab", "selectHighValueSubMenu", "getOrderType", "getMenuType", "getHighValueList", "uni", "title", "apiParams", "pageNum", "pageSize", "parentId", "menu", "userId", "quotationNum", "Object", "res", "console", "icon", "listData", "count", "getListByTab", "reset", "closeCate", "setTimeout", "chooseCate", "selectClick", "getCate", "ress", "seeDetail", "token", "platform", "targetUrl", "confirmText", "cancelText", "success", "url", "confirmDetail", "getList", "locationManager", "forceUpdate", "silent", "locationData", "defaultLocationData", "county", "address", "handleReceive", "confirmRe", "order_id", "duration", "goToSettle", "getServiceInfo", "city_id", "onReachBottom", "initializePage", "selectPriceRange", "validatePriceInput", "matchedPredefined", "validateDistanceInput", "applyFilters", "buildFilterParams", "toggleCollapse", "toggleFilter", "closeFilter", "toggleFilterPanel", "updateTabBadges", "selectActivityType", "applyActivityFilter", "resetActivityFilter", "selectArea", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset<PERSON>rea<PERSON><PERSON>er", "selectSort", "applySortFilter", "resetSortFilter", "applyAdvancedFilter", "resetAdvancedFilter", "fetchFilteredData", "applyPriceFilter", "resetPriceFilter", "applyDistanceFilter", "resetDistanceFilter", "applyCategoryFilter", "resetCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "getQuotationCounts", "onLoad", "scene", "onPullDownRefresh", "onShow", "needRefresh", "app", "globalNeedRefresh", "storage", "global", "onHide", "onUnload"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC0O72B;AAIA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC,UACA,gDACA,+CACA,8CACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC,WACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;MACAC,mBACA;QAAAF;QAAAG;QAAAF;MAAA,GACA;QAAAD;QAAAG;QAAAF;MAAA,EACA;MACAG;MACAC;QAAAL;MAAA;QAAAA;MAAA;QAAAA;MAAA;MACAM;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;QACAhC;QACAiC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;QACA/B;QAAA;QACAgC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAEAC;IACA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;QACA;MACA;;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EAAA,EACA;;EACAC;IACA;IACAC;MAKA;MAKA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;;MAEA;MACA;QAAA;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;gBACA;gBAAA;gBAEAC;kBACA9C;kBACAI;kBACA2C;kBACAC;kBACAC;kBACAlE;kBAAA;kBACAmE;kBAAA;kBACA/B;kBACAgC;kBACAC;gBACA,GAEA;gBACA;kBACAN;kBACAA;gBACA;;gBAEA;gBACAO;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAC;gBACA;kBACAX;oBACAY;oBACAX;kBACA;gBACA;;gBAEA;gBACAY;gBACA;gBACAC;gBACAd;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAW;gBACA;gBACA;cAAA;gBAAA;gBAEAX;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAf;kBACAC;gBACA;gBAAA;gBAEAC;kBACA9C;kBACAI;kBACA2C;kBACAC;kBACAC;kBACAlE;kBAAA;kBACAmE;kBAAA;kBACA/B;kBAAA;kBACAgC;kBAAA;kBACAC;gBACA,GAEA;;gBACA;kBACAN;kBACAA;gBACA;;gBAEA;gBACAO;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAC;gBACA;kBACAX;oBACAY;oBACAX;kBACA;gBACA;;gBAEA;gBACAY;gBACA;gBACAC;gBACAd;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAW;gBACA;gBACA;cAAA;gBAAA;gBAEAX;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgB;MACA;MACA;MACA;QACA7E;QACAgC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACA2C;MAAA;MACA;MACAC;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAZ;gBACAC;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;kBACAY;kBACAX;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAhB;gBACAiB;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC,wCAEA;gBACAC;gBACAC;gBAEA;kBACA;kBACAD;kBACAC;gBACA;;gBAEA;gBACA3B;kBACAC;kBACAvD;kBACAiF;kBACAC;kBACAC;oBACA;sBACA7B;wBACA8B;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMA;kBACAvB;gBACA;cAAA;gBAFAG;gBAGA;gBACAC;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACAX;kBACAY;kBACAX;gBACA;gBAAA;cAAA;gBAGA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAU;gBACAX;kBACAY;kBACAX;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA8B;MACA;QACApB;QACAX;QACAA;UACA8B;QACA;MACA;MACA;MACA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAhC;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGAgC;kBAAAC;kBAAAC;gBAAA;cAAA;gBAAAC;gBAEA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;oBACA;sBACAlD;sBACA1B;sBACAJ;oBACA;kBACA;kBAEAuD;gBACA;kBACA;kBACA;kBACA;kBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;gBACA;;gBAEA;gBACA0B;kBACAxE;kBACAC;kBACAwE;kBACAC;kBACAnF;kBACAI;gBACA;gBACAmD;gBACAX;cAAA;gBAAA;gBAAA;gBAAA,OAKA;kBACA5C;kBACAI;kBACA6C;kBACAF;kBACAC;kBACA7B;kBAAA;kBACA+B;kBAAA;kBACAnE;kBAAA;kBACAoE;kBAAA;kBACAC;gBACA;cAAA;gBAXAE;gBAYAC;gBACA;kBACAX;oBACAY;oBACAX;kBACA;gBACA;gBACAU;gBACA;gBACAE;gBACA;gBACAC;gBACAd;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAW;gBACA;gBACA;cAAA;gBAAA;gBAEAX;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAwC;MACA;MACA;MACA;MACA;QAAA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;MACA;QACA;QACA1C;UACAY;UACAX;UACA0C;QACA;QACAzB;UACAlB;YACA8B;UACA;QACA;MACA;QACA9B;UACAY;UACAX;QACA;MACA;IACA;IACA2C;MACA;MACA5C;QACA8B;MACA;IACA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFApC;gBAGAC;gBACA;gBACA;kBAAA;gBAAA;gBACA;kBACA,gBACA,oFACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAoC;MAAA;MACA;MACA;MACA;MACA;QACA5C;QACAC;QACAC;QACA7C;QACAJ;QACAmB;QAAA;QACAgC;QAAA;QACAC;MACA;;MAEA;MACAN;MACAA;;MAEA;MACA;QACAA;QACAA;MACA;;MAEA;MACAO;QAAA;MAAA;MAEA;QACA;UACAE;UACA;UACAX;YACAY;YACAX;UACA;UACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;QACAD;UACAY;UACAX;QACA;MACA;IACA;IACA+C;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAhD;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;;gBAEA;gBACA;kBACA;gBACA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAU;gBACAX;kBACAY;kBACAX;gBACA;cAAA;gBAAA;gBAEAD;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAiD;MACA;QACA9G;QACAgC;QACAC;QACAC;QACAC;MACA;IACA;IAEA4E;MACA;QACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;;QAEA;QACA,wBACA;UAAA/E;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,EACA;QACA;QACA;UAAA;UACA;YACA;YACA+E;YACA;UACA;QACA;QACA;UACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACApD;UACAY;UACAX;QACA;MACA;QACA;QACAD;UACAY;UACAX;QACA;MACA;QACA;MACA;IACA;IAEA;IACAoD;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAlG;QACAI;QACA2C;QAAA;QACAC;QACAC;QACA9B;QACAgC;QAAA;QACAC;MACA;;MAEA;MACAN;MACAA;;MAEA;MACA;QACAA;QACAA;MACA;;MAEA;MACAO;QAAA;MAAA;MAEA;IACA;IAEA;IACA8C;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;MACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA3D;MACA;MACA;MACA;IACA;IACA4D;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACA9D;QACA;MACA;MACA;MACA;IACA;IACA+D;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACAjE;QACA;MACA;MACA;MACA;IACA;IACAkE;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACAnI;QACAgC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAiG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAvE;kBACAC;gBACA;gBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAS;gBACAC;gBACA;kBACAX;oBACAY;oBACAX;kBACA;gBACA;;gBAEA;gBACAY;gBACA;gBACAC;gBACAd;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAW;gBACA;gBACA;cAAA;gBAAA;gBAEAX;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAwE;MACA;MACA;IACA;IAEAC;MACA;QACAtI;QACAgC;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACAoG;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAApE;gBACA;kBACAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACAoE;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAEAC;cACArE;cACA;gBACA;gBACAX;gBACAW;cACA;gBACAA;cACA;cACA;gBACAA;cACA;cACA;gBACAA;gBACA;gBACA;kBACA;kBACA;oBAAA;kBAAA;kBACAA;gBACA;kBACA;kBACAA;gBACA;cACA;gBACAA;gBACA;cACA;cAcA;gBACA;cACA;cACA;gBACA;kBACAX;oBACAY;oBACAX;kBACA;gBACA;kBACA;gBACA;cAEA;cAEA;gBACA;cACA;;cAEA;cACAD;gBACAW;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;cAEA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAsE;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACA;cACA;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OAGA;YAAA;cAEAjF;gBACAC;gBACAW;gBACA+B;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAhC;cACAX;gBACAC;gBACAW;cACA;YAAA;cAAA;cAEAZ;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAkF;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACAvE;;cAEA;cACAO;gBAAA;gBAAA;kBAAA;oBAAA;sBAAA;wBACAiE;wBACAC;wBACAC;wBAEA1E;0BACA2E;0BACAC;wBACA;wBAAA,MAEAJ;0BAAA;0BAAA;wBAAA;wBACAxE;wBACA;wBACAX;wBACA;0BACAoF;wBACA;wBACA;wBACA;wBACA;wBAAA;wBAAA,OACA;sBAAA;wBAAA;wBAAA,OACA;sBAAA;wBACAzE;sBAAA;sBAAA;wBAAA;oBAAA;kBAAA;gBAAA;cAAA,CAEA;;cAEA;cACA;gBACAJ;cACA;gBACAI;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACA6E;IACAxF;EACA;EACAyF;IACAzF;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACj2CA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/shifuIndex.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/shifuIndex.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shifuIndex.vue?vue&type=template&id=5a79bfc8&scoped=true&\"\nvar renderjs\nimport script from \"./shifuIndex.vue?vue&type=script&lang=js&\"\nexport * from \"./shifuIndex.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shifuIndex.vue?vue&type=style&index=0&id=5a79bfc8&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5a79bfc8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/shifuIndex.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=template&id=5a79bfc8&scoped=true&\"", "var components\ntry {\n  components = {\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-notice-bar/u-notice-bar\" */ \"uview-ui/components/u-notice-bar/u-notice-bar.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g1 = item.mobile.slice(0, 3)\n    return {\n      $orig: $orig,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.list.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.confirmshow = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.masterModalShow = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.detailModalShow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=script&lang=js&\"", "<template>\n\n\t<view class=\"page\">\n\n\t\t<tabbar :cur=\"0\"></tabbar>\n\t\t<view class=\"img\">\n\t\t\t<u-swiper :list=\"list1\" height=\"108\"></u-swiper>\n\t\t</view>\n\t\t\t\t<u-notice-bar :text=\"text1\"></u-notice-bar>\n\t\t<view class=\"location-bar\">\n\t\t\t<view class=\"location-info\">\n\t\t\t\t<view class=\"location-text\">当前接单位置：{{ province + city + district || '定位中...' }}</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"tabs-container\">\n\t\t\t<view class=\"tabs-header\">\n\t\t\t\t<view class=\"custom-tabs\">\n\t\t\t\t\t<view  class=\"tab-item\"   :class=\"{ 'active': currentTab === index }\" v-for=\"(tab, index) in tabsList\"\n\t\t\t\t\t\t:key=\"index\"   @click=\"switchTab(index)\">\n\t\t\t\t\t\t<text class=\"tab-text\">{{ tab.name }}</text>\n\t\t\t\t\t\t<view class=\"tab-badge\" v-if=\"tab.badge && tab.badge > 0\">{{ tab.badge }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-container-inline\">\n\t\t\t\t\t<view class=\"filter-toggle-btn\" @click=\"toggleFilterPanel\"\n\t\t\t\t\t\t:class=\"{ 'active': showFilterPanel }\">\n\t\t\t\t\t\t<text>筛选</text>\n\t\t\t\t\t\t<text class=\"filter-icon\">⚙</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 高价值子菜单 -->\n\t\t\t<view class=\"high-value-submenu\" v-if=\"showHighValueMenu\">\n\t\t\t\t<view class=\"submenu-item\" v-for=\"(item, index) in highValueSubMenu\" :key=\"index\"\n\t\t\t\t\t@click=\"selectHighValueSubMenu(item)\">\n\t\t\t\t\t<text>{{ item.name }}</text>\n\t\t\t\t\t<view class=\"submenu-badge\" v-if=\"item.badge && item.badge > 0\">{{ item.badge }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\n\t\t<!-- 筛选面板 -->\n\t\t<view class=\"filter-panel\" v-if=\"showFilterPanel\">\n\t\t\t<!-- 价格筛选 - 只在非报价订单时显示 -->\n\t\t\t<view class=\"filter-item-container\" v-if=\"currentTab !== 1\">\n\t\t\t\t<view class=\"filter-item\" @click.stop=\"toggleFilter('price')\"\n\t\t\t\t\t:class=\"{ 'active-filter-item': isPriceFilterActive }\">\n\t\t\t\t\t<text>{{ priceFilterText }}</text>\n\t\t\t\t\t<text class=\"arrow\" :class=\"{ 'rotate': showFilter === 'price' }\">▼</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"filter-item-container\">\n\t\t\t\t<view class=\"filter-item\" @click.stop=\"toggleFilter('distance')\"\n\t\t\t\t\t:class=\"{ 'active-filter-item': isDistanceFilterActive }\">\n\t\t\t\t\t<text>订单距离 {{ distance }}公里</text>\n\t\t\t\t\t<text class=\"arrow\" :class=\"{ 'rotate': showFilter === 'distance' }\">▼</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"filter-item-container\">\n\t\t\t\t<view class=\"filter-item\" @click.stop=\"toggleFilter('category')\"\n\t\t\t\t\t:class=\"{ 'active-filter-item': isCategoryFilterActive }\">\n\t\t\t\t\t<text>{{ currentCateName || '分类筛选' }}</text>\n\t\t\t\t\t<text class=\"arrow\" :class=\"{ 'rotate': showFilter === 'category' }\">▼</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 价格筛选下拉菜单 -->\n\t\t\t<view class=\"filter-dropdown\" v-if=\"showFilter === 'price'\" @click.stop>\n\t\t\t\t<view class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"filter-section\">\n\t\t\t\t\t\t<view class=\"section-title\">价格范围</view>\n\t\t\t\t\t\t<view class=\"option-list\">\n\t\t\t\t\t\t\t<view  class=\"option-item\"  \n\t\t\t\t\t\t\t\t:class=\"{ active: priceRange.min === 0 && priceRange.max === 100 && priceRange.type === 'predefined' }\"\n\t\t\t\t\t\t\t\t@click=\"selectPriceRange(0, 100, 'predefined')\">0 - 100</view>\n\t\t\t\t\t\t\t<view  class=\"option-item\"  \n\t\t\t\t\t\t\t\t:class=\"{ active: priceRange.min === 100 && priceRange.max === 200 && priceRange.type === 'predefined' }\"\n\t\t\t\t\t\t\t\t@click=\"selectPriceRange(100, 200, 'predefined')\">100 - 200</view>\n\t\t\t\t\t\t\t<view  class=\"option-item\"  \n\t\t\t\t\t\t\t\t:class=\"{ active: priceRange.min === 200 && priceRange.max === 500 && priceRange.type === 'predefined' }\"\n\t\t\t\t\t\t\t\t@click=\"selectPriceRange(200, 500, 'predefined')\">200 - 500</view>\n\t\t\t\t\t\t\t<view  class=\"option-item\"  \n\t\t\t\t\t\t\t\t:class=\"{ active: priceRange.min === 500 && priceRange.max === null && priceRange.type === 'predefined' }\"\n\t\t\t\t\t\t\t\t@click=\"selectPriceRange(500, null, 'predefined')\">500以上</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"filter-section\">\n\t\t\t\t\t\t<view class=\"section-title\">自定义价格</view>\n\t\t\t\t\t\t<view class=\"custom-price-inputs\">\n\t\t\t\t\t\t\t<input  type=\"digit\"   v-model=\"priceRange.customMin\"   placeholder=\"最低价\"  \n\t\t\t\t\t\t\t\t@blur=\"validatePriceInput('min')\" />\n\t\t\t\t\t\t\t<text>-</text>\n\t\t\t\t\t\t\t<input  type=\"digit\"   v-model=\"priceRange.customMax\"   placeholder=\"最高价\"  \n\t\t\t\t\t\t\t\t@blur=\"validatePriceInput('max')\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"filter-actions\">\n\t\t\t\t\t\t<view class=\"filter-btn reset\" @click=\"resetPriceFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"filter-btn confirm\" @click=\"applyPriceFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"filter-dropdown\" v-if=\"showFilter === 'distance'\" @click.stop>\n\t\t\t\t<view class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"filter-section\">\n\t\t\t\t\t\t<view class=\"section-title\">距离范围</view>\n\t\t\t\t\t\t<view class=\"distance-input\">\n\t\t\t\t\t\t\t<text>请输入距离范围（公里）</text>\n\t\t\t\t\t\t\t<view class=\"distance-input-container\">\n\t\t\t\t\t\t\t\t<input type=\"digit\" v-model=\"distance\" placeholder=\"请输入距离\"\n\t\t\t\t\t\t\t\t\t@blur=\"validateDistanceInput\" />\n\t\t\t\t\t\t\t\t<text class=\"unit\">公里</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"distance-hint\">范围：1-100公里</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"filter-actions\">\n\t\t\t\t\t\t<view class=\"filter-btn reset\" @click=\"resetDistanceFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"filter-btn confirm\" @click=\"applyDistanceFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"filter-dropdown\" v-if=\"showFilter === 'category'\" @click.stop>\n\t\t\t\t<view class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"filter-section\">\n\t\t\t\t\t\t<view class=\"section-title\">服务品类</view>\n\t\t\t\t\t\t<view class=\"option-list\">\n\t\t\t\t\t\t\t<view class=\"option-item\" :class=\"{ active: currentCateId === '' }\"\n\t\t\t\t\t\t\t\t@click=\"selectClick({ id: '', name: '全部' })\">全部</view>\n\t\t\t\t\t\t\t<view  class=\"option-item\"   v-for=\"(cate, index) in cateList\"   :key=\"index\"  \n\t\t\t\t\t\t\t\t:class=\"{ active: currentCateId === cate.id }\" @click=\"selectClick(cate)\">\n\t\t\t\t\t\t\t\t{{ cate.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"filter-actions\">\n\t\t\t\t\t\t<view class=\"filter-btn reset\" @click=\"resetCategoryFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"filter-btn confirm\" @click=\"applyCategoryFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"check_box\" v-if=\"false\">\n\t\t</view>\n\t\t<u-empty mode=\"order\" icon=\"http://cdn.uviewuni.com/uview/empty/order.png\" v-if=\"list.length == 0\"></u-empty>\n\t\t<view class=\"re_item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"seeDetail(item)\">\n\t\t\t<view class=\"top\">\n\t\t\t\t<image :src=\"item.goodsCover\" style=\"width: 160rpx;height: 160rpx;border-radius: 10rpx;\"></image>\n\t\t\t\t<view class=\"order\">\n\t\t\t\t\t<div class=\"title\">{{ item.goodsName }}<span v-if=\"item.type != 0\"\n\t\t\t\t\t\t\tstyle=\"font-size: 24rpx;color:#999;margin-left: 10rpx;\">(报价0.00元起)</span></div>\n\t\t\t\t\t<div class=\"price\">{{ item.type == 0 ? (item.payPrice ? `￥${item.payPrice}` : '￥***') : '待报价' }}\n\t\t\t\t\t</div>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"info\" @click=\"dingyue()\">\n\t\t\t\t<view class=\"address\">\n\t\t\t\t\t<u-icon name=\"map-fill\" color=\"#2979ff\" size=\"22\"></u-icon>\n\t\t\t\t\t<view class=\"right\">\n\t\t\t\t\t\t<view class=\"address_name\">{{ item.address }}</view>\n\t\t\t\t\t\t<view class=\"address_Info\">{{ item.addressInfo }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tel\">\n\t\t\t\t\t<u-icon name=\"phone-fill\" color=\"#2979ff\" size=\"22\"></u-icon>\n\t\t\t\t\t<view class=\"right\">{{ item.mobile.slice(0, 3) + '********' }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"notes\" v-if=\"item.text\">\n\t\t\t\t<text style=\"color:#999999;\">备注:</text>\n\t\t\t\t{{ item.text }}\n\t\t\t</view>\n\t\t\t<view class=\"order-stats\">\n\t\t\t\t<view class=\"stats-row\">\n\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t<text class=\"stat-label\">数量:</text>\n\t\t\t\t\t\t<text class=\"stat-value\">{{ item.num || 0 }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t<text class=\"stat-label\">当前报价人数:</text>\n\t\t\t\t\t\t<text class=\"stat-value\">{{ item.quotationNum || 0 }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-row\">\n\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t<text class=\"stat-label\">剩余报价人数:</text>\n\t\t\t\t\t\t<text class=\"stat-value\">{{ item.remainingNumber || 0 }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-item\" v-if=\"item.quotationSortboolean !== undefined\">\n\t\t\t\t\t\t<text class=\"stat-label\">报价状态:</text>\n\t\t\t\t\t\t<text class=\"stat-value\" :style=\"{ color: item.quotationSortboolean ? '#2979ff' : '#999' }\">\n\t\t\t\t\t\t\t{{ item.quotationSortboolean ? '已报价' : '未报价' }}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"btn\" :style=\"item.type == 1 ? '' : 'background-color:#2E80FE;color:#fff;'\"\n\t\t\t\************=\"seeDetail(item)\">\n\t\t\t\t{{ item.type == 1 ? '立即报价' : '立即接单' }}\n\t\t\t</view>\n\t\t</view>\n\t\t<u-modal :show=\"confirmshow\" :content=\"content\" showCancelButton @confirm=\"confirmRe\"\n\t\t\t@cancel=\"confirmshow = false\"></u-modal>\n\n\t\t<u-modal :show=\"masterModalShow\" content=\"您还不是师傅,请去入驻\" showCancelButton @confirm=\"goToSettle\"\n\t\t\t@cancel=\"masterModalShow = false\"></u-modal>\n\n\t\t<u-modal :show=\"detailModalShow\" title=\"服务承诺\" showCancelButton cancelText=\"不同意\" confirmText=\"同意\"\n\t\t\t@confirm=\"confirmDetail\" @cancel=\"detailModalShow = false\"\n\t\t\tv-if=\"shifustutus.data !== -2 && shifustutus.data !== -1\">\n\t\t\t<view class=\"modal-content\">\n\t\t\t\t<rich-text :nodes=\"getconfigs ? getconfigs : configInfo.shifuQualityCommitment\"></rich-text>\n\t\t\t</view>\n\t\t</u-modal>\n\n\t\t<view class=\"loadmore\" v-if=\"list.length >= 10\">\n\t\t\t<u-loadmore :status=\"status\" />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport tabbar from \"@/components/tabbarsf.vue\";\nimport {\n\tmapState,\n\tmapActions\n} from 'vuex';\nimport locationManager from '@/utils/location-manager.js';\nexport default {\n\tcomponents: {\n\t\ttabbar\n\t},\n\tdata() {\n\t\treturn {\n\t\t\torderData: '',\n\t\t\tshowDingyue: false,\n\t\t\tshowCate: false, // This seems redundant with the new filter dropdown, consider removal\n\t\t\tinfodata: '',\n\t\t\ttmplIds: [\n\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t],\n\t\t\tstatus: 'loadmore',\n\t\t\tid: '',\n\t\t\tshifuId: '',\n\t\t\t// 新增的选项卡数据\n\t\t\tcurrentTab: 0,\n\t\t\ttabsList: [\n\t\t\t\t{ name: '一口价', badge: '5' },\n\t\t\t\t{ name: '报价订单', badge: '11' },\n\t\t\t\t{ name: '高价值', badge: '' }\n\t\t\t],\n\t\t\t// 高价值子菜单\n\t\t\thighValueSubMenu: [\n\t\t\t\t{ name: '一口价', type: 0, badge: 0 },\n\t\t\t\t{ name: '报价订单', type: 1, badge: 0 }\n\t\t\t],\n\t\t\tshowHighValueMenu: false,\n\t\t\tlistType: [{ name: '一口价' }, { name: '报价订单' }, { name: '高价值 ' }],\n\t\t\tlist: [],\n\t\t\tconfirmshow: false,\n\t\t\tmasterModalShow: false,\n\t\t\tdetailModalShow: false,\n\t\t\tcontent: '确认接下该订单吗',\n\t\t\tinput: '',\n\t\t\tarea_id: '',\n\t\t\tlimit: 10, // Changed to 10 for more realistic pagination in examples\n\t\t\tpage: 1,\n\t\t\tbannerList: [],\n\t\t\tconfigInfo: '',\n\t\t\tgetconfigs: '',\n\t\t\tlist1: [],\n\t\t\ttext1: '', // 公告内容\n\t\t\tlng: '',\n\t\t\tshifustutus: {\n\t\t\t\tdata: 0,\n\t\t\t\tmsg: ''\n\t\t\t},\n\t\t\tQuotationCounts: '',\n\t\t\tlat: '',\n\t\t\tcateList: [],\n\t\t\tcurrentCateId: '',\n\t\t\tcurrentCateName: '分类筛选', // Changed default to '分类筛选' for consistency\n\t\t\tcopyCateList: [],\n\t\t\tprovince: '',\n\t\t\tcity: '',\n\t\t\tdistrict: '',\n\t\t\tisPageLoaded: false,\n\t\t\tselectedItem: null,\n\t\t\t// 新增的数据\n\t\t\tpriceRange: {\n\t\t\t\ttype: 'all', // 'all', 'predefined', 'custom'\n\t\t\t\tmin: null,\n\t\t\t\tmax: null,\n\t\t\t\tcustomMin: '',\n\t\t\t\tcustomMax: ''\n\t\t\t},\n\t\t\tdistance: 20, // 默认20公里\n\t\t\tappliedDistance: 20, // To track applied distance for filter bar text\n\t\t\t// 折叠面板状态控制 - These seem unused with the new filter dropdown, consider removal\n\t\t\tisPriceCollapsed: true,\n\t\t\tisDistanceCollapsed: true,\n\t\t\tisCategoryCollapsed: true,\n\t\t\t// 筛选相关\n\t\t\tshowFilter: null,\n\t\t\tshowFilterPanel: false, // 控制筛选面板显示\n\t\t\tcurrentActivityType: null,\n\n\t\t\tareaCount: 0\n\t\t};\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tconfigInfos: (state) => state.config.configInfo,\n\t\t\tregeocode: (state) => state.service.regeocode,\n\t\t\trefreshReceiving: (state) => state.service.refreshReceiving || '',\n\t\t}),\n\t\tpriceFilterText() {\n\t\t\tif (this.priceRange.type === 'all' || (this.priceRange.customMin === '' && this.priceRange.customMax === '')) {\n\t\t\t\treturn '价格区间';\n\t\t\t} else if (this.priceRange.type === 'predefined') {\n\t\t\t\treturn `${this.priceRange.min} - ${this.priceRange.max === null ? '500以上' : this.priceRange.max}`;\n\t\t\t} else if (this.priceRange.type === 'custom') {\n\t\t\t\tlet min = this.priceRange.customMin || '最低价';\n\t\t\t\tlet max = this.priceRange.customMax || '最高价';\n\t\t\t\tif (this.priceRange.customMin && this.priceRange.customMax) {\n\t\t\t\t\treturn `${min} - ${max}`;\n\t\t\t\t} else if (this.priceRange.customMin) {\n\t\t\t\t\treturn `${min}以上`;\n\t\t\t\t} else if (this.priceRange.customMax) {\n\t\t\t\t\treturn `${max}以下`;\n\t\t\t\t}\n\t\t\t\treturn '自定义价格'; // Fallback\n\t\t\t}\n\t\t\treturn '价格区间';\n\t\t},\n\t\tisPriceFilterActive() {\n\t\t\treturn this.priceRange.type !== 'all';\n\t\t},\n\t\tisDistanceFilterActive() {\n\t\t\treturn this.distance !== 20; // Assuming 20km is the default/reset state\n\t\t},\n\t\tisCategoryFilterActive() {\n\t\t\treturn this.currentCateId !== ''; // Assuming empty string is the default/reset state for '全部'\n\t\t}\n\t},\n\tmethods: {\n\t\t// 检测当前平台\n\t\tgetCurrentPlatform() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\treturn 'app-plus';\n\t\t\t// #endif\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\treturn 'mp-weixin';\n\t\t\t// #endif\n\t\t\t// #ifdef H5\n\t\t\treturn 'h5';\n\t\t\t// #endif\n\t\t\treturn 'unknown';\n\t\t},\n\t\ttextclick() {\n\t\t\tthis.$api.shifu.gettext()\n\t\t},\n\t\t// 新增的选项卡切换方法\n\t\tswitchTab(index) {\n\t\t\tthis.currentTab = index;\n\n\t\t\t// 如果点击的是高价值选项卡，显示子菜单\n\t\t\tif (index === 2) { // 高价值选项卡\n\t\t\t\tthis.showHighValueMenu = !this.showHighValueMenu;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 隐藏高价值子菜单\n\t\t\tthis.showHighValueMenu = false;\n\n\t\t\t// 根据选项卡切换不同的数据\n\t\t\tthis.page = 1;\n\t\t\tthis.list = []; // Clear list on tab switch\n\t\t\tthis.getListByTab(index);\n\t\t\tthis.closeFilter(); // Close any open filter dropdown\n\t\t},\n\n\t\t// 高价值子菜单点击\n\t\tselectHighValueSubMenu(subItem) {\n\t\t\tthis.showHighValueMenu = false;\n\t\t\tthis.page = 1;\n\t\t\tthis.list = [];\n\t\t\t// 使用高价值菜单类型和子项类型\n\t\t\tthis.getHighValueList(subItem.type);\n\t\t\tthis.closeFilter();\n\t\t},\n\n\t\t// Helper to get order type based on current tab\n\t\tgetOrderType(tabIndex) {\n\t\t\tswitch (tabIndex) {\n\t\t\t\tcase 0: return 0; // 一口价\n\t\t\t\tcase 1: return 1; // 报价订单\n\t\t\t\tcase 2: return undefined; // 高价值\n\t\t\t\tdefault: return undefined;\n\t\t\t}\n\t\t},\n\n\t\t// Helper to get menu type based on current tab\n\t\tgetMenuType(tabIndex) {\n\t\t\tswitch (tabIndex) {\n\t\t\t\tcase 2: return 2; // 高价值\n\t\t\t\tdefault: return 1; // 其他tab\n\t\t\t}\n\t\t},\n\n\t\t// 获取高价值订单数据\n\t\tasync getHighValueList(subType) {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中'\n\t\t\t});\n\t\t\ttry {\n\t\t\t\tlet apiParams = {\n\t\t\t\t\tlng: this.lng,\n\t\t\t\t\tlat: this.lat,\n\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\tpageSize: this.limit,\n\t\t\t\t\tparentId: this.currentCateId || 0,\n\t\t\t\t\ttype: subType, // 使用子菜单的类型\n\t\t\t\t\tmenu: 2, // 高价值菜单类型\n\t\t\t\t\tdistance: this.appliedDistance,\n\t\t\t\t\tuserId: this.infodata.userId,\n\t\t\t\t\tquotationNum: true\n\t\t\t\t};\n\n\t\t\t\t// 只有在非报价订单时才添加价格筛选参数\n\t\t\t\tif (subType !== 1) {\n\t\t\t\t\tapiParams.minPrice = this.priceRange.type !== 'all' ? this.priceRange.min : undefined;\n\t\t\t\t\tapiParams.maxPrice = this.priceRange.type !== 'all' ? this.priceRange.max : undefined;\n\t\t\t\t}\n\n\t\t\t\t// Clean up undefined values from apiParams\n\t\t\t\tObject.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);\n\n\t\t\t\tconst res = await this.$api.shifu.indexQuote(apiParams);\n\t\t\t\tconsole.log(res)\n\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t}, 3000);\n\t\t\t\t}\n\n\t\t\t\t// 更新数据结构处理\n\t\t\t\tconst listData = res.data.pageResult ? res.data.pageResult.list : (res.data.list || []);\n\t\t\t\tthis.$set(this, 'list', listData);\n\t\t\t\tlet count = this.list.length;\n\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\tthis.status = (listData && listData.length < this.limit) ? 'nomore' : 'loadmore';\n\n\t\t\t\t// 更新选项卡徽章数量\n\t\t\t\tthis.updateTabBadges(res.data);\n\t\t\t\tthis.$forceUpdate();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Error fetching high value list:\", error);\n\t\t\t\tthis.$set(this, 'list', []);\n\t\t\t\tthis.status = 'nomore';\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\n\t\t// 根据选项卡获取不同的数据\n\t\tasync getListByTab(tabIndex) {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中'\n\t\t\t});\n\t\t\ttry {\n\t\t\t\tlet apiParams = {\n\t\t\t\t\tlng: this.lng,\n\t\t\t\t\tlat: this.lat,\n\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\tpageSize: this.limit,\n\t\t\t\t\tparentId: this.currentCateId || 0,\n\t\t\t\t\ttype: this.getOrderType(tabIndex), // Set type based on tab\n\t\t\t\t\tmenu: this.getMenuType(tabIndex), // Set menu based on tab\n\t\t\t\t\tdistance: this.appliedDistance, // Use appliedDistance\n\t\t\t\t\tuserId: this.infodata.userId, // Assuming userId is available in infodata\n\t\t\t\t\tquotationNum: true // As per requirement\n\t\t\t\t};\n\n\t\t\t\t// 只有在非报价订单时才添加价格筛选参数\n\t\t\t\tif (tabIndex !== 1) {\n\t\t\t\t\tapiParams.minPrice = this.priceRange.type !== 'all' ? this.priceRange.min : undefined;\n\t\t\t\t\tapiParams.maxPrice = this.priceRange.type !== 'all' ? this.priceRange.max : undefined;\n\t\t\t\t}\n\n\t\t\t\t// Clean up undefined values from apiParams\n\t\t\t\tObject.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);\n\n\t\t\t\tconst res = await this.$api.shifu.indexQuote(apiParams);\n\t\t\t\tconsole.log(res)\n\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t}, 3000);\n\t\t\t\t}\n\n\t\t\t\t// 更新数据结构处理\n\t\t\t\tconst listData = res.data.pageResult ? res.data.pageResult.list : (res.data.list || []);\n\t\t\t\tthis.$set(this, 'list', listData);\n\t\t\t\tlet count = this.list.length;\n\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\tthis.status = (listData && listData.length < this.limit) ? 'nomore' : 'loadmore';\n\n\t\t\t\t// 更新选项卡徽章数量\n\t\t\t\tthis.updateTabBadges(res.data);\n\t\t\t\tthis.$forceUpdate();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Error fetching list by tab:\", error);\n\t\t\t\tthis.$set(this, 'list', []);\n\t\t\t\tthis.status = 'nomore';\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\n\t\treset() {\n\t\t\tthis.currentCateName = '分类筛选';\n\t\t\tthis.currentCateId = '';\n\t\t\tthis.priceRange = {\n\t\t\t\ttype: 'all',\n\t\t\t\tmin: null,\n\t\t\t\tmax: null,\n\t\t\t\tcustomMin: '',\n\t\t\t\tcustomMax: ''\n\t\t\t};\n\t\t\tthis.distance = 20;\n\t\t\tthis.appliedDistance = 20;\n\t\t\tthis.page = 1;\n\t\t\tthis.getListByTab(this.currentTab);\n\t\t},\n\n\t\t// This closeCate method seems redundant if using the new filter dropdown system\n\t\tcloseCate() {\n\t\t\tthis.showCate = false;\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.cateList = this.copyCateList;\n\t\t\t}, 500);\n\t\t},\n\t\t// This chooseCate method seems redundant if using the new filter dropdown system\n\t\tchooseCate() {\n\t\t\tthis.showCate = !this.showCate;\n\t\t},\n\n\t\tasync selectClick(cate) {\n\t\t\t// Only update the selected category without immediately applying the filter\n\t\t\t// The filter will be applied when '确定' is clicked in the category dropdown\n\t\t\tthis.currentCateName = cate.name;\n\t\t\tthis.currentCateId = cate.id;\n\t\t},\n\t\tasync getCate() {\n\t\t\ttry {\n\t\t\t\tconst ress = await this.$api.shifu.serviceCate();\n\t\t\t\tlet res = ress.data\n\t\t\t\tconsole.log(res)\n\t\t\t\tthis.cateList = res || [];\n\t\t\t\tthis.copyCateList = res || []; // Keep a copy if you need to reset the list later\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '获取分类失败'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tasync seeDetail(item) {\n\t\t\t// 先检查用户是否已登录\n\t\t\tconst userId = uni.getStorageSync('userId');\n\t\t\tconst token = uni.getStorageSync('token');\n\n\t\t\tif (!userId || !token) {\n\t\t\t\t// 检测当前平台\n\t\t\t\tconst platform = this.getCurrentPlatform();\n\n\t\t\t\t// 根据平台决定跳转页面\n\t\t\t\tlet targetUrl = '/pages/login'; // APP默认跳转登录页\n\t\t\t\tlet confirmText = '去登录';\n\n\t\t\t\tif (platform === 'mp-weixin') {\n\t\t\t\t\t// 小程序跳转到个人中心页面\n\t\t\t\t\ttargetUrl = '/pages/mine';\n\t\t\t\t\tconfirmText = '去登录';\n\t\t\t\t}\n\n\t\t\t\t// 用户未登录，显示提示并跳转\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '请先登录后再进行操作',\n\t\t\t\t\tconfirmText: confirmText,\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: targetUrl\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Fetch shifu status before opening any modal\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.getshifstutas({\n\t\t\t\t\tuserId: userId\n\t\t\t\t});\n\t\t\t\tthis.shifustutus = res;\n\t\t\t\tconsole.log(res);\n\t\t\t\tthis.selectedItem = item;\n\n\t\t\t\t// 判断师傅状态\n\t\t\t\tif (this.shifustutus.data === -1) {\n\t\t\t\t\t// 不是师傅，显示入驻提示\n\t\t\t\t\tthis.masterModalShow = true;\n\t\t\t\t\treturn;\n\t\t\t\t} else if (this.shifustutus.data === 1 || this.shifustutus.data === 4) {\n\t\t\t\t\t// 其他状态显示相应提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: this.shifustutus.msg || '无法进行此操作'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t} else {\n\t\t\t\t\t// 正常状态，显示服务承诺弹窗\n\t\t\t\t\tthis.detailModalShow = true;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Error checking shifu status:\", error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '检查身份失败'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tconfirmDetail() {\n\t\t\tif (this.selectedItem) {\n\t\t\t\tconsole.log(this.selectedItem)\n\t\t\t\tuni.setStorageSync('selectedOrder', this.selectedItem);\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/shifu/master_order_details?id=${this.selectedItem.id}&goodsId=${this.selectedItem.goodsId}&type=${this.selectedItem.type}`\n\t\t\t\t});\n\t\t\t}\n\t\t\tthis.detailModalShow = false;\n\t\t\tthis.selectedItem = null;\n\t\t},\n\t\tasync getList() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中'\n\t\t\t});\n\t\t\ttry {\n\t\t\t\t// 使用统一的定位管理器，避免重复调用\n\t\t\t\tconst locationData = await locationManager.getLocation({ forceUpdate: false, silent: true });\n\n\t\t\t\tif (locationData) {\n\t\t\t\t\t// 设置页面显示的地理位置信息\n\t\t\t\t\tthis.lng = locationData.lng || '';\n\t\t\t\t\tthis.lat = locationData.lat || '';\n\t\t\t\t\tthis.province = locationData.province || '';\n\t\t\t\t\tthis.city = locationData.city || '';\n\t\t\t\t\tthis.district = locationData.district || '';\n\n\t\t\t\t\t// 更新 Vuex store\n\t\t\t\t\tif (locationData.regeocode) {\n\t\t\t\t\t\tthis.$store.dispatch('setRegeocode', {\n\t\t\t\t\t\t\tregeocode: locationData.regeocode,\n\t\t\t\t\t\t\tlat: this.lat,\n\t\t\t\t\t\t\tlng: this.lng\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t\tconsole.log(\"定位获取成功:\", locationData);\n\t\t\t\t} else {\n\t\t\t\t\t// 如果定位失败，使用默认值\n\t\t\t\t\tthis.province = '安徽省';\n\t\t\t\t\tthis.city = '合肥市';\n\t\t\t\t\tthis.district = '蜀山区';\n\t\t\t\t\tconsole.log(\"使用默认定位信息\");\n\t\t\t\t}\n\t\t\t} catch (locationError) {\n\t\t\t\tconsole.error(\"定位获取失败:\", locationError);\n\t\t\t\tthis.province = '安徽省'; // Default province\n\t\t\t\tthis.city = '阜阳市'; // Default city\n\t\t\t\tthis.district = '临泉县'; // Default district\n\n\t\t\t\t// 保存默认地理位置数据\n\t\t\t\tconst defaultLocationData = {\n\t\t\t\t\tprovince: this.province,\n\t\t\t\t\tcity: this.city,\n\t\t\t\t\tcounty: this.district,\n\t\t\t\t\taddress: `${this.province}${this.city}${this.district}`,\n\t\t\t\t\tlng: this.lng || '',\n\t\t\t\t\tlat: this.lat || ''\n\t\t\t\t};\n\t\t\t\tconsole.log(defaultLocationData);\n\t\t\t\tuni.setStorageSync('locationData', defaultLocationData);\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// Initial list fetch will correspond to the default tab (一口价, tabIndex 0)\n\t\t\t\tconst res = await this.$api.shifu.indexQuote({\n\t\t\t\t\tlng: this.lng,\n\t\t\t\t\tlat: this.lat,\n\t\t\t\t\tparentId: 0,\n\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\tpageSize: this.limit,\n\t\t\t\t\tdistance: this.appliedDistance, // Ensure initial load uses appliedDistance\n\t\t\t\t\tmenu: this.getMenuType(this.currentTab), // Include menu for initial load\n\t\t\t\t\ttype: this.getOrderType(this.currentTab), // Include type for initial load (一口价)\n\t\t\t\t\tuserId: this.infodata.userId, // Assuming userId is available in infodata\n\t\t\t\t\tquotationNum: true // As per requirement\n\t\t\t\t});\n\t\t\t\tconsole.log(res);\n\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t}, 3000);\n\t\t\t\t}\n\t\t\t\tconsole.log(res);\n\t\t\t\t// 更新数据结构处理\n\t\t\t\tconst listData = res.data.pageResult ? res.data.pageResult.list : (res.data.list || []);\n\t\t\t\tthis.$set(this, 'list', listData);\n\t\t\t\tlet count = this.list.length;\n\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\tthis.status = (listData && listData.length < this.limit) ? 'nomore' : 'loadmore';\n\n\t\t\t\t// 更新选项卡徽章数量\n\t\t\t\tthis.updateTabBadges(res.data);\n\t\t\t\tthis.$forceUpdate();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Error fetching initial list:\", error);\n\t\t\t\tthis.$set(this, 'list', []);\n\t\t\t\tthis.status = 'nomore';\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\thandleReceive(item) {\n\t\t\t// this.textsss(); // Assuming this is defined elsewhere or not critical\n\t\t\tthis.orderData = item;\n\t\t\tthis.id = item.id;\n\t\t\tif (item.type == 0) { // Assuming type 0 means fixed price that can be directly received\n\t\t\t\tthis.confirmshow = true;\n\t\t\t}\n\t\t},\n\t\tconfirmRe() {\n\t\t\tthis.confirmshow = false;\n\t\t\tthis.$api.shifu.rece_Order({\n\t\t\t\torder_id: this.id\n\t\t\t}).then(res => {\n\t\t\t\tthis.getList();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\ttitle: '接单成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/shifu/master_my_order'\n\t\t\t\t\t});\n\t\t\t\t}, 1000);\n\t\t\t}).catch(error => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'fail',\n\t\t\t\t\ttitle: error.message || '接单失败'\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tgoToSettle() {\n\t\t\tthis.masterModalShow = false;\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/shifu/Settle'\n\t\t\t});\n\t\t},\n\t\tasync getServiceInfo() {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.index({\n\t\t\t\t\tcity_id: this.area_id\n\t\t\t\t});\n\t\t\t\tconsole.log(res)\n\t\t\t\tthis.bannerList = res.data || [];\n\t\t\t\tthis.list1 = res.data.map(item => item.img) || [];\n\t\t\t\tif (!this.list1.length) {\n\t\t\t\t\tthis.list1 = [\n\t\t\t\t\t\t'https://zskj.asia/attachment/image/666/24/09/2bdd13fab41b42b987bcfc501aa535bb.jpg'\n\t\t\t\t\t];\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\t// uni.showToast({\n\t\t\t\t// \ticon: 'none',\n\t\t\t\t// \ttitle: '获取轮播图失败'\n\t\t\t\t// });\n\t\t\t\tthis.list1 = ['https://zskj.asia/attachment/image/666/24/09/e790eea3f21b4f48ab2b00b034468035.jpg'];\n\t\t\t}\n\t\t},\n\t\tonReachBottom() {\n\t\t\tif (this.status == 'nomore') return;\n\t\t\tthis.status = 'loading';\n\t\t\tthis.page++;\n\t\t\tlet apiParams = {\n\t\t\t\tpageNum: this.page,\n\t\t\t\tpageSize: this.limit,\n\t\t\t\tparentId: this.currentCateId || 0,\n\t\t\t\tlat: this.lat,\n\t\t\t\tlng: this.lng,\n\t\t\t\tdistance: this.appliedDistance, // Include appliedDistance for pagination\n\t\t\t\tuserId: this.infodata.userId, // Assuming userId is available in infodata\n\t\t\t\tquotationNum: true // As per requirement\n\t\t\t};\n\n\t\t\t// Apply the 'type' and 'menu' parameter based on the current tab for pagination\n\t\t\tapiParams.type = this.getOrderType(this.currentTab);\n\t\t\tapiParams.menu = this.getMenuType(this.currentTab);\n\n\t\t\t// 只有在非报价订单时才添加价格筛选参数\n\t\t\tif (this.currentTab !== 1 && this.priceRange.type !== 'all') {\n\t\t\t\tapiParams.minPrice = this.priceRange.min;\n\t\t\t\tapiParams.maxPrice = this.priceRange.max;\n\t\t\t}\n\n\t\t\t// Clean up undefined values from apiParams\n\t\t\tObject.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);\n\n\t\t\tthis.$api.shifu.indexQuote(apiParams).then(res => {\n\t\t\t\tif (!res.data || !res.data.list || res.data.list.length === 0) {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '没有更多数据了'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// 更新分页数据结构处理\n\t\t\t\tconst listData = res.data.pageResult ? res.data.pageResult.list : (res.data.list || []);\n\t\t\t\tthis.$set(this, 'list', [...this.list, ...listData]);\n\t\t\t\tif (listData.length < this.limit) {\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t} else {\n\t\t\t\t\tthis.status = 'loadmore';\n\t\t\t\t}\n\t\t\t}).catch(error => {\n\t\t\t\tthis.status = 'nomore';\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '加载失败，请稍后重试'\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tasync initializePage() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '初始化中'\n\t\t\t});\n\t\t\ttry {\n\t\t\t\t// const systemInfo = uni.getSystemInfoSync(); // This line is not used\n\t\t\t\tawait this.getServiceInfo();\n\t\t\t\tawait this.getCate();\n\t\t\t\tawait this.getList(); // This will load the '一口价' initially\n\n\t\t\t\t// Get userId from storage if available\n\t\t\t\tif (uni.getStorageSync('shiInfo')) {\n\t\t\t\t\tthis.infodata = JSON.parse(uni.getStorageSync('shiInfo'));\n\t\t\t\t}\n\n\t\t\t\tthis.configInfo = uni.getStorageSync('configInfo');\n\t\t\t\tthis.$forceUpdate();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Error initializing page:\", error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '初始化失败，请稍后重试'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t// 新增的方法\n\t\tselectPriceRange(min, max, type) {\n\t\t\tthis.priceRange = {\n\t\t\t\ttype: type,\n\t\t\t\tmin: min,\n\t\t\t\tmax: max,\n\t\t\t\tcustomMin: min !== null ? min.toString() : '',\n\t\t\t\tcustomMax: max !== null ? max.toString() : ''\n\t\t\t};\n\t\t},\n\n\t\tvalidatePriceInput(type) {\n\t\t\tif (type === 'min') {\n\t\t\t\tconst min = parseFloat(this.priceRange.customMin);\n\t\t\t\tif (!isNaN(min) && min >= 0) {\n\t\t\t\t\tthis.priceRange.min = min;\n\t\t\t\t} else {\n\t\t\t\t\tthis.priceRange.customMin = '';\n\t\t\t\t\tthis.priceRange.min = null;\n\t\t\t\t}\n\t\t\t} else if (type === 'max') {\n\t\t\t\tconst max = parseFloat(this.priceRange.customMax);\n\t\t\t\tif (!isNaN(max) && max > 0) {\n\t\t\t\t\tthis.priceRange.max = max;\n\t\t\t\t} else {\n\t\t\t\t\tthis.priceRange.customMax = '';\n\t\t\t\t\tthis.priceRange.max = null;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Determine priceRange.type based on custom inputs or predefined selection\n\t\t\tconst hasCustomMin = this.priceRange.customMin !== '';\n\t\t\tconst hasCustomMax = this.priceRange.customMax !== '';\n\n\t\t\tif (hasCustomMin || hasCustomMax) {\n\t\t\t\tthis.priceRange.type = 'custom';\n\n\t\t\t\t// Check if custom inputs match any predefined range for highlighting\n\t\t\t\tconst predefinedRanges = [\n\t\t\t\t\t{ min: 0, max: 100 },\n\t\t\t\t\t{ min: 100, max: 200 },\n\t\t\t\t\t{ min: 200, max: 500 },\n\t\t\t\t\t{ min: 500, max: null }\n\t\t\t\t];\n\t\t\t\tlet matchedPredefined = false;\n\t\t\t\tfor (const range of predefinedRanges) {\n\t\t\t\t\tif (this.priceRange.min === range.min && this.priceRange.max === range.max) {\n\t\t\t\t\t\tthis.priceRange.type = 'predefined';\n\t\t\t\t\t\tmatchedPredefined = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!matchedPredefined && (hasCustomMin || hasCustomMax)) {\n\t\t\t\t\tthis.priceRange.type = 'custom';\n\t\t\t\t}\n\t\t\t} else if (!this.priceRange.min && !this.priceRange.max) {\n\t\t\t\tthis.priceRange.type = 'all';\n\t\t\t} else if (this.priceRange.type !== 'predefined') {\n\t\t\t\t// Fallback if somehow min/max are set but custom inputs are empty and not predefined\n\t\t\t\tthis.priceRange.type = 'custom';\n\t\t\t}\n\t\t},\n\n\t\tvalidateDistanceInput() {\n\t\t\tconst distance = parseFloat(this.distance);\n\t\t\tif (isNaN(distance) || distance < 1) {\n\t\t\t\tthis.distance = 1;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '距离不能小于1公里'\n\t\t\t\t});\n\t\t\t} else if (distance > 100) {\n\t\t\t\tthis.distance = 100;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '距离不能大于100公里'\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthis.distance = Math.round(distance);\n\t\t\t}\n\t\t},\n\n\t\t// This applyFilters method is a general one; the specific apply methods (price, distance, category) will call fetchFilteredData\n\t\tapplyFilters() {\n\t\t\t// This method can be used if there was a single \"Apply All Filters\" button.\n\t\t\t// Since we have separate \"确定\" buttons for each dropdown, we will use\n\t\t\t// applyPriceFilter, applyDistanceFilter, applyCategoryFilter.\n\t\t\t// For now, it will just call the common fetchFilteredData with current state.\n\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\tthis.closeFilter();\n\t\t},\n\n\t\t// Adding a helper to build common API parameters\n\t\tbuildFilterParams() {\n\t\t\tlet apiParams = {\n\t\t\t\tlng: this.lng,\n\t\t\t\tlat: this.lat,\n\t\t\t\tpageNum: 1, // Always reset to page 1 on new filter application\n\t\t\t\tpageSize: this.limit,\n\t\t\t\tparentId: this.currentCateId || 0,\n\t\t\t\tdistance: this.appliedDistance,\n\t\t\t\tuserId: this.infodata.userId, // Assuming userId is available in infodata\n\t\t\t\tquotationNum: true // As per requirement\n\t\t\t};\n\n\t\t\t// Set type and menu based on the current active tab\n\t\t\tapiParams.type = this.getOrderType(this.currentTab);\n\t\t\tapiParams.menu = this.getMenuType(this.currentTab);\n\n\t\t\t// Add price range parameters - 只有在非报价订单时才添加\n\t\t\tif (this.currentTab !== 1 && this.priceRange.type !== 'all') {\n\t\t\t\tapiParams.minPrice = this.priceRange.min;\n\t\t\t\tapiParams.maxPrice = this.priceRange.max;\n\t\t\t}\n\n\t\t\t// Clean up undefined values from apiParams\n\t\t\tObject.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);\n\n\t\t\treturn apiParams;\n\t\t},\n\n\t\t// Adding collapse functionality (if still needed, though current UI uses show/hide)\n\t\ttoggleCollapse(type) {\n\t\t\tif (type === 'price') {\n\t\t\t\tthis.isPriceCollapsed = !this.isPriceCollapsed;\n\t\t\t} else if (type === 'distance') {\n\t\t\t\tthis.isDistanceCollapsed = !this.isDistanceCollapsed;\n\t\t\t} else if (type === 'category') {\n\t\t\t\tthis.isCategoryCollapsed = !this.isCategoryCollapsed;\n\t\t\t}\n\t\t},\n\t\t// 筛选相关方法\n\t\ttoggleFilter(filter) {\n\t\t\tif (this.showFilter === filter) {\n\t\t\t\tthis.showFilter = null;\n\t\t\t} else {\n\t\t\t\tthis.showFilter = filter;\n\t\t\t}\n\t\t},\n\n\t\tcloseFilter() {\n\t\t\tthis.showFilter = null;\n\t\t},\n\n\t\t// 切换筛选面板显示\n\t\ttoggleFilterPanel() {\n\t\t\tthis.showFilterPanel = !this.showFilterPanel;\n\t\t\tif (!this.showFilterPanel) {\n\t\t\t\tthis.showFilter = null; // 关闭筛选面板时也关闭下拉菜单\n\t\t\t}\n\t\t},\n\n\t\t// 更新选项卡徽章数量\n\t\tupdateTabBadges(data) {\n\t\t\tif (data) {\n\t\t\t\t// 更新选项卡徽章\n\t\t\t\tthis.tabsList[0].badge = data.onePriceCount || 0; // 一口价\n\t\t\t\tthis.tabsList[1].badge = data.parityCount || 0; // 报价订单\n\t\t\t\tthis.tabsList[2].badge = data.heightCount || 0; // 高价值\n\n\t\t\t\t// 更新高价值子菜单徽章\n\t\t\t\tthis.highValueSubMenu[0].badge = data.heightOnePriceCount || 0; // 高价值一口价\n\t\t\t\tthis.highValueSubMenu[1].badge = data.heightParityCount || 0; // 高价值报价订单\n\t\t\t}\n\t\t},\n\t\t// These activity, area, and sort related methods seem to be remnants and aren't tied to the current UI\n\t\t// They should be removed if not implemented in the template.\n\t\tselectActivityType(item) {\n\t\t\tthis.currentActivityType = item.value;\n\t\t},\n\t\tapplyActivityFilter() {\n\t\t\tlet apiParams = this.buildFilterParams();\n\t\t\tif (this.currentActivityType) {\n\t\t\t\tapiParams.activityType = this.currentActivityType;\n\t\t\t}\n\t\t\tthis.fetchFilteredData(apiParams);\n\t\t\tthis.closeFilter();\n\t\t},\n\t\tresetActivityFilter() {\n\t\t\tthis.currentActivityType = '';\n\t\t},\n\t\tselectArea(item) {\n\t\t\tthis.currentArea = item.name;\n\t\t},\n\t\tapplyAreaFilter() {\n\t\t\tlet apiParams = this.buildFilterParams();\n\t\t\tif (this.currentArea) {\n\t\t\t\tconst selectedArea = this.areaList.find(item => item.name === this.currentArea);\n\t\t\t\tif (selectedArea) {\n\t\t\t\t\tapiParams.area = selectedArea.value;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.fetchFilteredData(apiParams);\n\t\t\tthis.closeFilter();\n\t\t},\n\t\tresetAreaFilter() {\n\t\t\tthis.currentArea = '';\n\t\t},\n\t\tselectSort(item) {\n\t\t\tthis.currentSort = item.name;\n\t\t},\n\t\tapplySortFilter() {\n\t\t\tlet apiParams = this.buildFilterParams();\n\t\t\tif (this.currentSort) {\n\t\t\t\tconst selectedSort = this.sortOptions.find(item => item.name === this.currentSort);\n\t\t\t\tif (selectedSort) {\n\t\t\t\t\tapiParams.sort = selectedSort.value;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.fetchFilteredData(apiParams);\n\t\t\tthis.closeFilter();\n\t\t},\n\t\tresetSortFilter() {\n\t\t\tthis.currentSort = '';\n\t\t},\n\n\t\tapplyAdvancedFilter() {\n\t\t\t// This method is redundant if filters are applied individually.\n\t\t\t// If it's for a \"master\" apply button, it would call fetchFilteredData with current state.\n\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\tthis.closeFilter();\n\t\t},\n\n\t\tresetAdvancedFilter() {\n\t\t\tthis.priceRange = {\n\t\t\t\ttype: 'all',\n\t\t\t\tmin: null,\n\t\t\t\tmax: null,\n\t\t\t\tcustomMin: '',\n\t\t\t\tcustomMax: ''\n\t\t\t};\n\t\t\tthis.distance = 20;\n\t\t\tthis.appliedDistance = 20;\n\t\t\tthis.currentCateId = '';\n\t\t\tthis.currentCateName = '分类筛选';\n\t\t\tthis.page = 1; // Reset page on full reset\n\t\t\tthis.fetchFilteredData(this.buildFilterParams()); // Apply reset\n\t\t\tthis.closeFilter();\n\t\t},\n\n\t\t// 统一的数据获取方法\n\t\tasync fetchFilteredData(apiParams) {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中'\n\t\t\t});\n\t\t\tthis.list = []; // Clear list before fetching new data\n\t\t\tthis.page = 1; // Ensure page is reset when new filters are applied\n\n\t\t\t// Ensure userId is consistently added if infodata is available\n\t\t\t// if (this.infodata && this.infodata.userId) {\n\t\t\t// \tapiParams.userId = this.infodata.userId;\n\t\t\t// }\n\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.indexQuote(apiParams);\n\t\t\t\tconsole.log(res)\n\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t}, 3000);\n\t\t\t\t}\n\n\t\t\t\t// 更新数据结构处理\n\t\t\t\tconst listData = res.data.pageResult ? res.data.pageResult.list : (res.data.list || []);\n\t\t\t\tthis.$set(this, 'list', listData);\n\t\t\t\tlet count = this.list.length;\n\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\tthis.status = (listData && listData.length < this.limit) ? 'nomore' : 'loadmore';\n\n\t\t\t\t// 更新选项卡徽章数量\n\t\t\t\tthis.updateTabBadges(res.data);\n\t\t\t\tthis.$forceUpdate();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Error fetching filtered data:\", error);\n\t\t\t\tthis.$set(this, 'list', []);\n\t\t\t\tthis.status = 'nomore';\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t// 价格筛选相关方法\n\t\tapplyPriceFilter() {\n\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\tthis.closeFilter();\n\t\t},\n\n\t\tresetPriceFilter() {\n\t\t\tthis.priceRange = {\n\t\t\t\ttype: 'all',\n\t\t\t\tmin: null,\n\t\t\t\tmax: null,\n\t\t\t\tcustomMin: '',\n\t\t\t\tcustomMax: ''\n\t\t\t};\n\t\t\tthis.applyPriceFilter(); // Apply reset immediately\n\t\t},\n\n\t\t// 距离筛选相关方法\n\t\tapplyDistanceFilter() {\n\t\t\tthis.appliedDistance = this.distance; // Update applied distance\n\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\tthis.closeFilter();\n\t\t},\n\n\t\tresetDistanceFilter() {\n\t\t\tthis.distance = 20; // Reset slider value\n\t\t\tthis.appliedDistance = 20; // Reset applied value\n\t\t\tthis.applyDistanceFilter(); // Apply reset immediately\n\t\t},\n\n\t\t// 分类筛选相关方法\n\t\tapplyCategoryFilter() {\n\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\tthis.closeFilter();\n\t\t},\n\n\t\tresetCategoryFilter() {\n\t\t\tthis.currentCateId = '';\n\t\t\tthis.currentCateName = '分类筛选';\n\t\t\tthis.applyCategoryFilter(); // Apply reset immediately\n\t\t},\n\n\t\t// 获取接单统计数据\n\t\tasync getQuotationCounts() {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.getQuotationCounts();\n\t\t\t\tif (res.code === '-1') {\n\t\t\t\t\tconsole.error('获取接单统计失败:', res.msg);\n\t\t\t\t} else {\n\t\t\t\t\tthis.QuotationCounts = res.data;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取接单统计失败:', error);\n\t\t\t}\n\t\t},\n\t},\n\tasync onLoad(query) {\n\t\t\n\t\tconst scene = decodeURIComponent(query.scene || '');\n\t\tconsole.log('开始获取 scene:', scene);\n\t\tif (scene) {\n\t\t\tthis.$store.commit('setErweima', scene);\n\t\t\tuni.setStorageSync('erweima', scene);\n\t\t\tconsole.log('已存储 scene:', scene);\n\t\t} else {\n\t\t\tconsole.log('未获取到 scene 参数');\n\t\t}\n\t\t\tthis.$api.shifu.checkCoachService().then(res=>{\n\t\t\t\tconsole.log(res)\n\t\t\t})\n\t\tthis.$api.shifu.gonggao().then(res=>{\n\t\t\t\tconsole.log('公告数据:', res);\n\t\t\t\t// 显示所有公告的content内容\n\t\t\t\tif(res.data && res.data.length > 0) {\n\t\t\t\t\t// 将所有公告的content连接起来显示\n\t\t\t\t\tthis.text1 = res.data.map(item => item.content).join(' | ')\n\t\t\t\t\tconsole.log('设置公告内容:', this.text1);\n\t\t\t\t} else {\n\t\t\t\t\tthis.text1 = ''\n\t\t\t\t\tconsole.log('没有公告数据');\n\t\t\t\t}\n\t\t\t}).catch(error => {\n\t\t\t\tconsole.error('获取公告失败:', error);\n\t\t\t\tthis.text1 = '';\n\t\t\t})\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\tthis.$api.base.getConfig().then(res => {\n\t\t\tthis.getconfigs = res.data.shifuQualityCommitment\n\t\t})\n\t\tthis.$api.shifu.getQuotationCounts().then(res => {\n\t\t\tif (res.code === '-1') {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: res.msg\n\t\t\t\t}, 3000);\n\t\t\t} else {\n\t\t\t\tthis.QuotationCounts = res.data\n\t\t\t}\n\n\t\t});\n\n\t\tif (uni.getStorageSync('shiInfo')) {\n\t\t\tthis.infodata = JSON.parse(uni.getStorageSync('shiInfo'));\n\t\t}\n\n\t\t// 注册刷新事件监听器\n\t\tuni.$on('refreshReceivingList', () => {\n\t\t\tconsole.log('收到刷新事件，开始刷新页面数据');\n\t\t\tthis.page = 1;\n\t\t\tthis.list = [];\n\t\t\tthis.getListByTab(this.currentTab);\n\t\t\t// 同时刷新接单统计数据\n\t\t\tthis.getQuotationCounts();\n\t\t});\n\n\t\tthis.isPageLoaded = true;\n\t\tawait this.initializePage();\n\t},\n\tasync onPullDownRefresh() {\n\t\ttry {\n\t\t\t// 重置页面状态\n\t\t\tthis.page = 1;\n\t\t\tthis.list = [];\n\t\t\tthis.status = 'loadmore';\n\n\t\t\t// 重新获取数据\n\t\t\tawait this.getListByTab(this.currentTab);\n\n\t\t\t// 刷新接单统计\n\t\t\tawait this.getQuotationCounts();\n\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '刷新成功',\n\t\t\t\ticon: 'success',\n\t\t\t\tduration: 1000\n\t\t\t});\n\t\t} catch (error) {\n\t\t\tconsole.error('下拉刷新失败:', error);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '刷新失败，请重试',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t} finally {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}\n\t},\n\tasync onShow() {\n\t\tconsole.log('shifuIndex onShow 触发');\n\n\t\t// 延迟检查刷新标志，确保 onUnload 已经执行\n\t\tsetTimeout(async () => {\n\t\t\tconst needRefresh = uni.getStorageSync('needRefreshShifuIndex');\n\t\t\tconst app = getApp();\n\t\t\tconst globalNeedRefresh = app && app.globalData && app.globalData.needRefreshShifuIndex;\n\n\t\t\tconsole.log('检查刷新标志:', {\n\t\t\t\tstorage: needRefresh,\n\t\t\t\tglobal: globalNeedRefresh\n\t\t\t});\n\n\t\t\tif (needRefresh || globalNeedRefresh) {\n\t\t\t\tconsole.log('检测到刷新标志，开始刷新页面数据');\n\t\t\t\t// 清除刷新标志\n\t\t\t\tuni.removeStorageSync('needRefreshShifuIndex');\n\t\t\t\tif (app && app.globalData) {\n\t\t\t\t\tapp.globalData.needRefreshShifuIndex = false;\n\t\t\t\t}\n\t\t\t\t// 刷新页面数据\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.list = [];\n\t\t\t\tawait this.getListByTab(this.currentTab);\n\t\t\t\tawait this.getQuotationCounts();\n\t\t\t\tconsole.log('页面数据刷新完成');\n\t\t\t}\n\t\t}, 100);\n\n\t\t// Ensure shifu status is checked on show if not already done, or if it needs to be refreshed\n\t\tthis.$api.shifu.getshifstutas({\n\t\t\tuserId: uni.getStorageSync('userId')\n\t\t}).then(res => {\n\t\t\tconsole.log(res)\n\t\t\tthis.shifustutus = res;\n\t\t});\n\t},\n\tonHide() {\n\t\tuni.$off('refreshReceivingList'); // Unregister event listener when page hides\n\t},\n\tonUnload() {\n\t\tuni.$off('refreshReceivingList'); // Unregister event listener when page unloads\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tmin-height: 100vh;\n\tbackground-color: #f8f9fa;\n\tpadding-bottom: 120rpx;\n}\n\n.img {\n\twidth: 690rpx;\n\tmargin: 20rpx auto;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n}\n\n.location-bar {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 16rpx 20rpx;\n\tbackground-color: #fff;\n\tmargin: 0 20rpx;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.location-info {\n\tflex: 1;\n}\n\n.location-text {\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.tabs-container {\n\tmargin: 20rpx;\n\tbackground-color: #fff;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.tabs-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 0 20rpx;\n}\n\n.custom-tabs {\n\tdisplay: flex;\n\tflex: 1;\n}\n\n.tab-item {\n\tflex: 1;\n\tflex-direction: column;\n\tpadding: 20rpx 0;\n\ttext-align: center;\n\tposition: relative;\n\tfont-size: 28rpx;\n\tcolor: #666;\n\ttransition: color 0.3s;\n\n\t&.active {\n\t\tcolor: #2E80FE;\n\t\tfont-weight: 600;\n\n\t\t&::after {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\tbottom: 0;\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%);\n\t\t\twidth: 40rpx;\n\t\t\theight: 4rpx;\n\t\t\tbackground-color: #2E80FE;\n\t\t\tborder-radius: 2rpx;\n\t\t}\n\t}\n}\n\n.tab-badge {\n\tposition: absolute;\n\ttop: 8rpx;\n\tright: 8rpx;\n\tmin-width: 32rpx;\n\theight: 32rpx;\n\tline-height: 32rpx;\n\tbackground-color: #ff4757;\n\tcolor: #fff;\n\tfont-size: 20rpx;\n\ttext-align: center;\n\tborder-radius: 16rpx;\n\tpadding: 0 8rpx;\n\ttransform: scale(0.8);\n}\n\n.high-value-submenu {\n\tdisplay: flex;\n\tjustify-content: center;\n\tgap: 40rpx;\n\tpadding: 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-top: 1rpx solid #eee;\n\tanimation: slideDown 0.3s ease-out;\n}\n\n.submenu-item {\n\tpadding: 12rpx 24rpx;\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tborder: 1rpx solid #ddd;\n\ttransition: all 0.3s;\n\tposition: relative;\n\n\t&:active {\n\t\tbackground-color: #2E80FE;\n\t\tcolor: #fff;\n\t\tborder-color: #2E80FE;\n\t}\n}\n\n.submenu-badge {\n\tposition: absolute;\n\ttop: -8rpx;\n\tright: -8rpx;\n\tmin-width: 32rpx;\n\theight: 32rpx;\n\tline-height: 32rpx;\n\tbackground-color: #ff4757;\n\tcolor: #fff;\n\tfont-size: 20rpx;\n\ttext-align: center;\n\tborder-radius: 16rpx;\n\tpadding: 0 8rpx;\n\ttransform: scale(0.8);\n}\n\n@keyframes slideDown {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(-10rpx);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n.filter-container-inline {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.filter-container-inline .filter-toggle-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tpadding: 12rpx 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tborder: 1rpx solid #ddd;\n\ttransition: all 0.3s;\n\n\t&.active {\n\t\tbackground-color: #2E80FE;\n\t\tcolor: #fff;\n\t\tborder-color: #2E80FE;\n\t}\n}\n\n.filter-icon {\n\tfont-size: 24rpx;\n}\n\n.submenu-filter-container {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-top: 1rpx solid #eee;\n\tanimation: slideDown 0.3s ease-out;\n}\n\n.filter-container-inline {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.filter-container-inline .filter-toggle-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tpadding: 12rpx 20rpx;\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tborder: 1rpx solid #ddd;\n\ttransition: all 0.3s;\n\n\t&.active {\n\t\tbackground-color: #2E80FE;\n\t\tcolor: #fff;\n\t\tborder-color: #2E80FE;\n\t}\n}\n\n.filter-container {\n\tbackground-color: #fff;\n\tpadding: 0 20rpx;\n\tposition: sticky;\n\ttop: 0;\n\tz-index: 10;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.filter-bar {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 12rpx 0;\n}\n\n.filter-left {\n\tflex: 1;\n}\n\n.filter-right {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.filter-toggle-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tpadding: 12rpx 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\ttransition: all 0.3s;\n\n\t&.active {\n\t\tbackground-color: #2E80FE;\n\t\tcolor: #fff;\n\t}\n}\n\n.filter-icon {\n\tfont-size: 24rpx;\n}\n\n.filter-panel {\n\tdisplay: block;\n\tpadding: 20rpx;\n\tbackground-color: #f8f9fa;\n\tborder-top: 1rpx solid #eee;\n\tanimation: slideDown 0.3s ease-out;\n\tposition: relative;\n}\n\n.filter-panel .filter-item-container {\n\tdisplay: inline-block;\n\tmargin-right: 16rpx;\n\tmargin-bottom: 16rpx;\n\tposition: relative;\n}\n\n.filter-item-container {\n\tflex: 1;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.filter-item {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tpadding: 8rpx 20rpx;\n\tborder-radius: 40rpx;\n\ttransition: background-color 0.3s, color 0.3s;\n\n\t&.active-filter-item {\n\t\tbackground-color: #e6f0ff;\n\t\tcolor: #2E80FE;\n\t}\n}\n\n.arrow {\n\tmargin-left: 8rpx;\n\tfont-size: 20rpx;\n\tcolor: #aaa;\n\ttransition: transform 0.3s ease;\n}\n\n.arrow.rotate {\n\ttransform: rotate(180deg);\n}\n\n.filter-dropdown {\n\tposition: absolute;\n\ttop: 100%;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n\tpadding: 24rpx;\n\tmargin-top: 8rpx;\n\tz-index: 1000;\n\tanimation: fadeIn 0.3s ease-out;\n}\n\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(-10rpx);\n\t}\n\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n.dropdown-content {\n\tmax-height: 60vh;\n\toverflow-y: auto;\n}\n\n.filter-section {\n\tmargin-bottom: 24rpx;\n}\n\n.section-title {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tmargin-bottom: 16rpx;\n}\n\n.option-list {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(3, 1fr);\n\tgap: 16rpx;\n}\n\n.option-item {\n\tpadding: 16rpx;\n\ttext-align: center;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n\ttransition: background-color 0.3s, color 0.3s;\n\n\t&.active {\n\t\tbackground-color: #2E80FE;\n\t\tcolor: #fff;\n\t}\n}\n\n.custom-price-inputs {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 12rpx;\n\tpadding: 8rpx;\n\n\tinput {\n\t\tflex: 1;\n\t\theight: 60rpx;\n\t\tpadding: 0 16rpx;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t}\n\n\ttext {\n\t\tcolor: #999;\n\t\tfont-size: 26rpx;\n\t\tpadding: 0 8rpx;\n\t}\n}\n\n.distance-input {\n\t>text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 12rpx;\n\t\tdisplay: block;\n\t}\n\n\t.distance-input-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 0 16rpx;\n\n\t\tinput {\n\t\t\tflex: 1;\n\t\t\theight: 60rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #333;\n\t\t\tbackground: transparent;\n\t\t\tborder: none;\n\t\t}\n\n\t\t.unit {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #666;\n\t\t}\n\t}\n\n\t.distance-hint {\n\t\tfont-size: 22rpx;\n\t\tcolor: #999;\n\t\tmargin-top: 8rpx;\n\t\ttext-align: right;\n\t}\n}\n\n.filter-actions {\n\tdisplay: flex;\n\tgap: 16rpx;\n\tmargin-top: 24rpx;\n}\n\n.filter-btn {\n\tflex: 1;\n\theight: 72rpx;\n\tline-height: 72rpx;\n\ttext-align: center;\n\tborder-radius: 36rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: opacity 0.3s;\n\n\t&.reset {\n\t\tbackground-color: #f8f9fa;\n\t\tcolor: #666;\n\t}\n\n\t&.confirm {\n\t\tbackground-color: #2E80FE;\n\t\tcolor: #fff;\n\t}\n}\n\n.quotation-counts {\n\tmargin: 20rpx;\n\tpadding: 20rpx;\n\tbackground-color: #fff;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.counts-header {\n\ttext-align: center;\n\tmargin-bottom: 16rpx;\n\n\t.counts-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n}\n\n.counts-row {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\talign-items: center;\n}\n\n.count-item {\n\ttext-align: center;\n\n\t.count-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 4rpx;\n\t}\n\n\t.count-value {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #2E80FE;\n\t}\n}\n\n.count-divider {\n\twidth: 1rpx;\n\theight: 40rpx;\n\tbackground-color: #eee;\n}\n\n.re_item {\n\tmargin: 20rpx;\n\tpadding: 24rpx;\n\tbackground-color: #fff;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.top {\n\tdisplay: flex;\n\tgap: 20rpx;\n\n\timage {\n\t\twidth: 160rpx;\n\t\theight: 160rpx;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.order {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\n\t\t.title {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333;\n\t\t\toverflow: hidden;\n\t\t\ttext-overflow: ellipsis;\n\t\t\twhite-space: nowrap;\n\t\t}\n\n\t\t.price {\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #2E80FE;\n\t\t}\n\t}\n}\n\n.info {\n\tmargin-top: 20rpx;\n\tpadding-top: 20rpx;\n\tborder-top: 1rpx solid #eee;\n\n\t.address,\n\t.tel {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: 12rpx;\n\n\t\t.left {\n\t\t\tmargin-right: 16rpx;\n\t\t\tpadding-top: 4rpx;\n\t\t}\n\n\t\t.right {\n\t\t\tflex: 1;\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #333;\n\n\t\t\t.address_name {\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\t.address_Info {\n\t\t\t\tcolor: #666;\n\t\t\t\tmargin-top: 4rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.notes {\n\tmargin-top: 16rpx;\n\tpadding: 16rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 8rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n\n\tview {\n\t\tcolor: #999;\n\t\tmargin-bottom: 8rpx;\n\t}\n}\n\n.order-stats {\n\tmargin-top: 16rpx;\n\tpadding: 16rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 8rpx;\n\n\t.stats-row {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 12rpx;\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\t}\n\n\t.stat-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8rpx;\n\n\t\t.stat-label {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #666;\n\t\t}\n\n\t\t.stat-value {\n\t\t\tfont-size: 26rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n}\n\n.btn {\n\tmargin-top: 20rpx;\n\theight: 72rpx;\n\tline-height: 72rpx;\n\ttext-align: center;\n\tborder-radius: 36rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #2E80FE;\n\tborder: 1rpx solid #2E80FE;\n\ttransition: background 0.3s;\n\n\t&[style*=\"background-color\"] {\n\t\tbackground-color: #2E80FE !important;\n\t\tcolor: #fff !important;\n\t\tborder: none;\n\t}\n}\n\n.loadmore {\n\tpadding: 20rpx 0;\n\ttext-align: center;\n}\n\n.modal-content {\n\tpadding: 20rpx;\n\tmax-height: 400rpx;\n\toverflow-y: auto;\n\tfont-size: 26rpx;\n\tline-height: 1.6;\n\tcolor: #333;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=style&index=0&id=5a79bfc8&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=style&index=0&id=5a79bfc8&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755735939581\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}