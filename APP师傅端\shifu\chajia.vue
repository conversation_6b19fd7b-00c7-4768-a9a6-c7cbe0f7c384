<template>
	<view class="page">
	

		<view class="content">
			<view class="order-info" v-if="orderInfo">
				<view class="order-header">
					<view class="order-no">订单号：{{ orderInfo.orderCode }}</view>
					<view class="order-status">{{ getOrderStatusText(orderInfo.payType) }}</view>
				</view>
				<view class="order-detail">
					<view class="goods-info">
						<image :src="orderInfo.goodsCover" class="goods-image"></image>
						<view class="goods-text">
							<text class="goods-name">{{ orderInfo.goodsName }}</text>
							<text class="goods-price">￥{{ orderInfo.coachServicePrice }}</text>
						</view>
					</view>
				</view>
			</view>

			<view class="form-container">
				<view class="form-title">申请差价</view>
				<u--form labelPosition="left" :model="diffApplyForm" :rules="diffApplyRules" ref="diffApplyForm">
					<u-form-item label="差价金额" prop="diffAmount" ref="item1">
						<u--input v-model="diffApplyForm.diffAmount" placeholder="请输入差价金额" type="number"
							border="none" @input="onDiffAmountChange"></u--input>
					</u-form-item>
	
					<u-form-item label="差价原因">
						<view class="reason-type-display">
							<text class="reason-type-text">配件不符合</text>
							<view class="reason-type-badge">类型: 1</view>
						</view>
					</u-form-item>
					<u-form-item label="原因详情" prop="reasonDetail" ref="item3">
						<u--textarea v-model="diffApplyForm.reasonDetail" placeholder="请输入差价原因详情"
							count></u--textarea>
					</u-form-item>
					<u-form-item label="配件质保时长" prop="partsWarrantyDuration" ref="item4">
						<view class="warranty-input-group">
							<u--input v-model="diffApplyForm.partsWarrantyDuration" placeholder="请输入质保天数"
								type="number" border="none"></u--input>
							<text class="unit-text">天</text>
						</view>
					</u-form-item>
					<u-form-item label="配件图" class="last-form-item" ref="item5">
						<view class="upload-container">
							<upload @upload="imgUploadDiff" @del="imgUploadDiff"
								:imagelist="diffApplyForm.partsimgs" imgtype="partsimgs" imgclass="parts-img"
								text="上传配件图" :imgsize="9"></upload>
						</view>
					</u-form-item>
				</u--form>
			</view>

			<!-- 费用明细显示区域 -->
			<u-form-item label="差价金额" prop="diffAmount" ref="item1">
				<u--input v-model="diffApplyForm.diffAmount" placeholder="请输入差价金额" type="number"
					border="none" @input="onDiffAmountChange"></u--input>
			</u-form-item>

			<transition name="fade-slide">
				<view class="fee-detail-container" v-if="diffApplyForm.diffAmount && parseFloat(diffApplyForm.diffAmount) > 0">
					<view class="fee-detail-title">费用明细</view>
					<view class="fee-detail-content">
						<view class="fee-item">
							<view class="fee-label">差价金额</view>
							<view class="fee-value primary">¥{{ formatAmount(diffApplyForm.diffAmount) }}</view>
						</view>
						<view class="fee-item">
							<view class="fee-label">服务费 ({{ commissionRate }}%)</view>
							<view class="fee-value danger">-¥{{ formatAmount(serviceFee) }}</view>
						</view>
						<view class="fee-divider"></view>
						<view class="fee-item total">
							<view class="fee-label">预估到手金额</view>
							<view class="fee-value success">¥{{ formatAmount(actualAmount) }}</view>
						</view>
					</view>
				</view>
			</transition>

			<view v-if="orderInfo && orderInfo.orderDiffPriceList && orderInfo.orderDiffPriceList.length > 0" class="sub_orders">
				<view class="sub_title">差价申请记录</view>
				<scroll-view class="sub_scroll_container" scroll-y="true" :style="{height: getScrollViewHeight(orderInfo.orderDiffPriceList.length) + 'rpx'}">
					<view class="sub_item" v-for="(diffItem, diffIndex) in orderInfo.orderDiffPriceList" :key="diffItem.id">
						<view class="sub_head">
							<view class="sub_no">差价单号：{{ diffItem.diffCode }}</view>
							<view class="sub_status">{{ getDiffStatusText(diffItem.status) }}</view>
						</view>
						<view class="sub_content">
							<view class="sub_info_grid">
								<view class="sub_info_row">
									<view class="sub_info_item">
										<text class="sub_label">差价金额：</text>
										<text class="sub_value sub_amount_value">￥{{ diffItem.diffAmount }}</text>
									</view>
									<view class="sub_info_item sub-warranty-info">
										<text class="sub-label">质保日期：</text>
										<view class="sub-warranty-details">
											<text class="sub-value sub-warranty-value">{{ formatWarrantyDate(diffItem.partsWarrantyPeriod) }}</text>
											<view class="remaining-days-badge" v-if="diffItem.partsWarrantyPeriod">
												<text>{{ getRemainingWarrantyDays(diffItem.partsWarrantyPeriod) }}</text>
											</view>
										</view>
									</view>
								</view>
								<view class="sub_info_row">
									<view class="sub_info_item sub_reason_item">
										<text class="sub_label">原因：</text>
										<text class="sub_value sub_reason_value">{{ diffItem.reasonDetail }}</text>
									</view>
								</view>
								<view class="sub_info_row">
									<view class="sub_info_item">
										<text class="sub_label">申请时间：</text>
										<text class="sub_value sub_time_value">{{ diffItem.createdTime }}</text>
									</view>
								</view>
							</view>
							<view class="sub_actions">
								<view class="sub_qzf" v-if="diffItem.status === 0"
									@click.stop="showDiffCancelModal(diffItem)">
									取消差价
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<view class="footer">
			<view class="btn-cancel" @click="goBack">取消</view>
			<view class="btn-confirm" @click="diffApplyConfirm">提交申请</view>
		</view>
	</view>
</template>

<script>
import Upload from '@/components/upload.vue'; // Import upload component

export default {
	components: {
		Upload,
	},
	data() {
		return {
			orderInfo: null, // 订单信息
			orderId: '', // 订单ID
			commissionRate: 0, // 服务费比例
			diffApplyForm: {
				diffAmount: '',
				reasonType: 1, // 差价原因类型，目前固定为1代表配件不符合
				reasonDetail: '',
				partsWarrantyDuration: '', // 新增：配件质保时长（天）
				partsimgs: [], // 配件图片
			},
			diffApplyRules: {
				diffAmount: [{
					required: true,
					message: '请输入差价金额',
					trigger: ['blur', 'change']
				}, {
					validator: (rule, value, callback) => {
						return value >= 0.01;
					},
					message: '差价金额必须大于等于0.01',
					trigger: ['blur', 'change']
				}],
				reasonDetail: [{
					required: true,
					message: '请输入差价原因详情',
					trigger: ['blur', 'change']
				}],
				partsWarrantyDuration: [{
					required: true,
					message: '请输入质保天数',
					trigger: ['blur', 'change']
				}, {
					validator: (rule, value, callback) => {
						return value > 0;
					},
					message: '质保天数必须大于0',
					trigger: ['blur', 'change']
				}],
			},
			pay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],
		}
	},
	computed: {
		// 计算服务费
		serviceFee() {
			const diffAmount = parseFloat(this.diffApplyForm.diffAmount) || 0;
			return (diffAmount * this.commissionRate / 100).toFixed(2);
		},
		// 计算实际到手金额
		actualAmount() {
			const diffAmount = parseFloat(this.diffApplyForm.diffAmount) || 0;
			const serviceFee = parseFloat(this.serviceFee) || 0;
			return (diffAmount - serviceFee).toFixed(2);
		}
	},
	onLoad(options) {
		// 获取服务费比例
		this.$api.shifu.getpercentageOfCommission().then(res=>{
			console.log(res)
			if (res.code === "200") {
				this.commissionRate = res.data || 0;
			}
		}).catch(err => {
			console.error('获取服务费比例失败:', err);
		});

		if (options.orderId) {
			this.orderId = options.orderId;
			this.loadOrderInfo();
		} else {
			uni.showToast({
				icon: 'none',
				title: '订单信息缺失'
			});
			setTimeout(() => {
				this.goBack();
			}, 1500);
		}
	},
	onReady() {
		this.$nextTick(() => {
			if (this.$refs.diffApplyForm && this.$refs.diffApplyForm.setRules) {
				this.$refs.diffApplyForm.setRules(this.diffApplyRules);
			}
		});
	},
	methods: {
		// 格式化金额显示
		formatAmount(amount) {
			const num = parseFloat(amount) || 0;
			return num.toFixed(2);
		},

		// 差价金额输入变化处理
		onDiffAmountChange(value) {
			// 可以在这里添加实时验证逻辑
			console.log('差价金额变化:', value);
		},

		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 加载订单信息
		async loadOrderInfo() {
			try {
				const orderDetails = uni.getStorageSync('orderdetails');
				if (orderDetails && orderDetails.id == this.orderId) {
					this.orderInfo = orderDetails;
				} else {
					uni.showToast({
						icon: 'none',
						title: '订单信息获取失败'
					});
				}
			} catch (error) {
				console.error('加载订单信息失败:', error);
				uni.showToast({
					icon: 'none',
					title: '订单信息获取失败'
				});
			}
		},

		// 获取订单状态文本
		getOrderStatusText(payType) {
			return this.pay_typeArr[payType] || '未知状态';
		},

		// 获取差价申请状态文本
		getDiffStatusText(status) {
			const statusMap = {
				'-1': '已取消',
				0: '待确认',
				1: '已确认待支付',
				2: '已支付',
				3: '已拒绝'
			};
			return statusMap[status] || '未知状态';
		},

		// 格式化配件质保日期
		formatWarrantyDate(timestamp) {
			if (!timestamp) {
				return '无质保信息';
			}
			try {
				const date = new Date(timestamp);
				if (isNaN(date.getTime())) {
					return '无效日期';
				}
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			} catch (error) {
				console.error('格式化质保日期失败:', error);
				return '格式错误';
			}
		},
		
		// 获取剩余质保天数
		getRemainingWarrantyDays(timestamp) {
			if (!timestamp) return '';
			const now = new Date();
			const warrantyDate = new Date(timestamp);
			
			if (isNaN(warrantyDate.getTime()) || warrantyDate < now) {
				return '已过期';
			}
			
			const diffTime = warrantyDate.getTime() - now.getTime();
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
			
			return `剩余${diffDays}天`;
		},

		// 获取滚动视图高度
		getScrollViewHeight(itemCount) {
			const maxHeight = 450;
			const itemHeight = 150;
			const calculatedHeight = Math.min(itemCount * itemHeight, maxHeight);
			return calculatedHeight;
		},

		// 处理差价申请中的图片上传
		imgUploadDiff(e) {
			const { imagelist, imgtype } = e;
			this.$set(this.diffApplyForm, imgtype, imagelist);
		},

		// 显示取消差价申请确认弹窗
		showDiffCancelModal(diffItem) {
			uni.showModal({
				title: '取消差价申请',
				content: `确定要取消差价单号 ${diffItem.diffCode} 的申请吗？`,
				confirmText: '确定',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.diffCancel(diffItem);
					}
				}
			});
		},

		// 取消差价申请
		async diffCancel(diffItem) {
			try {
				const res = await this.$api.shifu.diffCancel({
					id: diffItem.id
				});
				if (res.code === "200") {
					uni.showToast({
						title: '差价申请已取消',
						icon: 'success'
					});
					this.loadOrderInfo();
				} else {
					uni.showToast({
						title: res.msg || '取消失败',
						icon: 'none'
					});
				}
			} catch (err) {
				uni.showToast({
					title: '请求失败',
					icon: 'none'
				});
				console.error('Error in diffCancel:', err);
			}
		},

		// 提交差价申请
		async diffApplyConfirm() {
			try {
				if (!this.$refs.diffApplyForm || !this.$refs.diffApplyForm.validate) {
					uni.showToast({
						icon: 'none',
						title: '表单未准备就绪，请稍后重试'
					});
					return;
				}
				await this.$refs.diffApplyForm.validate();

				if (!this.orderInfo) {
					uni.showToast({
						icon: 'none',
						title: '订单信息缺失'
					});
					return;
				}

				const now = new Date();
				const partsWarrantyPeriodTimestamp = now.getTime() + (this.diffApplyForm.partsWarrantyDuration * 24 * 60 * 60 * 1000);

				try {
					const partsImgsString = this.diffApplyForm.partsimgs.map(img => img.path).join(',');

					const res = await this.$api.shifu.diffApply({
						orderId: this.orderInfo.id,
						diffAmount: parseFloat(this.diffApplyForm.diffAmount),
						reasonType: this.diffApplyForm.reasonType,
						reasonDetail: this.diffApplyForm.reasonDetail,
						partsImgs: partsImgsString,
						partsWarrantyPeriod: partsWarrantyPeriodTimestamp
					});

					if (res.code === "200") {
						uni.showToast({
							title: '差价申请成功',
							icon: 'success'
						});
						setTimeout(() => {
							this.goBack();
						}, 1500);
					} else {
						uni.showToast({
							title: res.msg || '差价申请失败',
							icon: 'none'
						});
					}
				} catch (err) {
					uni.showToast({
						title: '请求失败',
						icon: 'none'
					});
					console.error('Error in diffApply:', err);
				}
			} catch (errors) {
				uni.showToast({
					icon: 'none',
					title: '请检查填写信息'
				});
			}
		},
	}
}
</script>

<style scoped lang="scss">
.page {
	background-color: #F8F8F8;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
	height: 88rpx;
	background: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.back-icon {
			font-size: 36rpx;
			color: #333;
		}
	}

	.title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.placeholder {
		width: 60rpx;
	}
}

.content {

	padding: 88rpx 30rpx 20rpx;
}

.order-info {
	background: #FFFFFF;
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;

	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		.order-no {
			font-size: 24rpx;
			color: #666;
		}

		.order-status {
			font-size: 24rpx;
			color: #2E80FE;
			font-weight: 500;
		}
	}

	.order-detail {
		.goods-info {
			display: flex;
			align-items: center;

			.goods-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 12rpx;
				margin-right: 20rpx;
			}

			.goods-text {
				flex: 1;
				display: flex;
				flex-direction: column;

				.goods-name {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 10rpx;
				}

				.goods-price {
					font-size: 32rpx;
					color: #ff6b35;
					font-weight: 600;
				}
			}
		}
	}
}

.form-container {
	background: #FFFFFF;
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;

	.form-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 30rpx;
	}
}

// 费用明细样式
.fee-detail-container {
	margin-top: 30rpx;
	padding: 24rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
	border-radius: 16rpx;
	border: 1rpx solid #e8f0ff;
	box-shadow: 0 4rpx 20rpx rgba(46, 128, 254, 0.08);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4rpx;
		background: linear-gradient(90deg, #2E80FE 0%, #1a6fd1 50%, #2E80FE 100%);
	}

	.fee-detail-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;

		&::before {
			content: '';
			width: 6rpx;
			height: 20rpx;
			background: linear-gradient(135deg, #2E80FE 0%, #1a6fd1 100%);
			border-radius: 3rpx;
			margin-right: 12rpx;
		}
	}

	.fee-detail-content {
		.fee-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 16rpx 0;
			transition: all 0.3s ease;
			border-radius: 8rpx;

			&:hover {
				background: rgba(46, 128, 254, 0.05);
				transform: translateX(4rpx);
			}

			&.total {
				padding: 20rpx 16rpx 16rpx;
				font-weight: 600;
				background: linear-gradient(135deg, rgba(0, 178, 106, 0.1) 0%, rgba(0, 178, 106, 0.05) 100%);
				border-radius: 12rpx;
				margin: 8rpx -8rpx 0;
				position: relative;

				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 4rpx;
					height: 60%;
					background: linear-gradient(135deg, #00b26a 0%, #00a85a 100%);
					border-radius: 2rpx;
				}

				.fee-label {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
				}

				.fee-value {
					font-size: 34rpx;
					font-weight: 700;
					text-shadow: 0 1rpx 2rpx rgba(0, 178, 106, 0.2);
				}
			}

			.fee-label {
				font-size: 28rpx;
				color: #666;
				flex: 1;
			}

			.fee-value {
				font-size: 28rpx;
				font-weight: 500;
				text-align: right;

				&.primary {
					color: #2E80FE;
				}

				&.danger {
					color: #ff6b35;
				}

				&.success {
					color: #00b26a;
				}
			}
		}

		.fee-divider {
			height: 2rpx;
			background: linear-gradient(90deg, transparent 0%, rgba(46, 128, 254, 0.3) 20%, rgba(46, 128, 254, 0.6) 50%, rgba(46, 128, 254, 0.3) 80%, transparent 100%);
			margin: 20rpx 0;
			border-radius: 1rpx;
			position: relative;

			&::after {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 8rpx;
				height: 8rpx;
				background: #2E80FE;
				border-radius: 50%;
				box-shadow: 0 0 8rpx rgba(46, 128, 254, 0.4);
			}
		}
	}
}

.warranty-input-group {
	display: flex;
	align-items: center;
	
	.unit-text {
		margin-left: 20rpx;
		font-size: 28rpx;
		color: #333;
	}
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #FFFFFF;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 20rpx;

	.btn-cancel,
	.btn-confirm {
		flex: 1;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 8rpx;
		font-size: 28rpx;
		transition: all 0.2s;
	}

	.btn-cancel {
		background: #f8f8f8;
		color: #666;
	}
	
	.btn-confirm {
		background: #2E80FE;
		color: #fff;
	}

	.btn-cancel:active {
		background: #e8e8e8;
	}

	.btn-confirm:active {
		background: #1a6fd1;
	}
}

// 差价申请记录样式
.sub_orders {
	background: #FFFFFF;
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;

	.sub_title {
		font-size: 26rpx;
		font-weight: 500;
		color: #666;
		margin-bottom: 20rpx;
	}

	.sub_scroll_container {
		max-height: 450rpx;
		overflow-y: auto;
	}

	.sub_item {
		background: #FFFFFF;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 15rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

		.sub_head {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 15rpx;

			.sub_no {
				font-size: 22rpx;
				color: #666;
				max-width: 400rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.sub_status {
				font-size: 22rpx;
				color: #2E80FE;
				font-weight: 500;
			}
		}

		.sub_content {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;

			.sub_info_grid {
				flex: 1;
				margin-right: 20rpx;

				.sub_info_row {
					display: flex;
					margin-bottom: 12rpx;
					gap: 20rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.sub_info_item {
						display: flex;
						align-items: center;
						flex: 1;
						min-width: 0;

						&.sub_reason_item {
							flex: 2;
						}

						.sub_label {
							font-size: 20rpx;
							color: #999;
							margin-right: 8rpx;
							flex-shrink: 0;
						}

						.sub_value {
							font-size: 22rpx;
							color: #333;
							flex: 1;
							min-width: 0;

							&.sub_amount_value {
								font-weight: 500;
								color: #ff6b35;
							}

							&.sub_warranty-value {
								color: #2E80FE;
								font-weight: 500;
							}

							&.sub_reason_value {
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}

							&.sub_time_value {
								font-size: 20rpx;
								color: #999;
							}
						}
					}
				}
			}

			.sub_actions {
				flex-shrink: 0;
				align-self: flex-start;

				.sub_qzf {
					width: 120rpx;
					height: 40rpx;
					background: #ff6b6b;
					border-radius: 40rpx;
					font-size: 18rpx;
					font-weight: 400;
					line-height: 40rpx;
					text-align: center;
					color: #fff;
				}
			}
		}
	}
}

// 表单样式
.upload-container {
	margin-top: 16rpx;
	padding: 20rpx;
	border: 1rpx dashed #ccc;
	border-radius: 8rpx;
	background: #fafafa;
}

.parts-img {
	width: 120rpx;
	height: 120rpx;
	margin-right: 12rpx;
	margin-bottom: 12rpx;
	border-radius: 8rpx;
}

.reason-type-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx;
	background: #f8f8f8;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
}

.reason-type-text {
	font-size: 28rpx;
	color: #333;
}

.reason-type-badge {
	background: #2E80FE;
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

// 表单项样式优化
/deep/ .u-form-item {
	margin-bottom: 24rpx;
	padding-bottom: 20rpx;
}

/deep/ .u-form-item:not(.last-form-item) {
	border-bottom: 1rpx solid #f0f0f0;
}

/deep/ .u-form-item:last-child {
	margin-bottom: 0;
}

/deep/ .u-form-item__body {
	padding-left: 0 !important;
}

/deep/ .u-form-item__label {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 16rpx;
	font-weight: 600;
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: -8rpx;
		left: 0;
		width: 30rpx;
		height: 3rpx;
		background: linear-gradient(90deg, #2E80FE 0%, #1a6fd1 100%);
		border-radius: 2rpx;
	}
}

/deep/ .u--input__content {
	background: #f8f9ff;
	border: 1rpx solid #e8f0ff;
	border-radius: 12rpx;
	padding: 20rpx;
	font-size: 30rpx;
	color: #333;
	transition: all 0.3s ease;

	&:focus {
		background: #ffffff;
		border-color: #2E80FE;
		box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);
	}
}

/deep/ .u--textarea__content {
	background: #f8f8f8;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	padding: 16rpx;
	font-size: 28rpx;
	color: #333;
	min-height: 120rpx;
}

// 动画效果
.fade-slide-enter-active,
.fade-slide-leave-active {
	transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.fade-slide-enter-from {
	opacity: 0;
	transform: translateY(-20rpx);
}

.fade-slide-leave-to {
	opacity: 0;
	transform: translateY(-10rpx);
}
</style>