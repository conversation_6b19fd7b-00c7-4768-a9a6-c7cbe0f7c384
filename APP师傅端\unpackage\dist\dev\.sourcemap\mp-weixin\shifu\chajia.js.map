{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?d5b3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?88a1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?4614", "uni-app:///shifu/chajia.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?f42b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?f147"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Upload", "data", "orderInfo", "orderId", "commissionRate", "diffApplyForm", "diffAmount", "reasonType", "reasonDetail", "partsWarrantyDuration", "partsimgs", "diffApplyRules", "required", "message", "trigger", "validator", "pay_typeArr", "computed", "serviceFee", "actualAmount", "onLoad", "console", "uni", "icon", "title", "setTimeout", "onReady", "methods", "formatAmount", "onDiffAmountChange", "goBack", "loadOrderInfo", "orderDetails", "getOrderStatusText", "getDiffStatusText", "formatWarrantyDate", "getRemainingWarrantyDays", "getScrollViewHeight", "imgUploadDiff", "imgtype", "showDiffCancelModal", "content", "confirmText", "cancelText", "success", "diffCancel", "id", "res", "diffApplyConfirm", "now", "partsWarrantyPeriodTimestamp", "partsImgsString", "partsImgs", "partsWarrantyPeriod"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,mTAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,mTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Iz2B;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;MACA;;MACAC;QACAL;UACAM;UACAC;UACAC;QACA;UACAC;YACA;UACA;UACAF;UACAC;QACA;QACAN;UACAI;UACAC;UACAC;QACA;QACAL;UACAG;UACAC;UACAC;QACA;UACAC;YACA;UACA;UACAF;UACAC;QACA;MACA;MACAE;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;MACAC;MACA;QACA;MACA;IACA;MACAA;IACA;IAEA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAR;IACA;IAEA;IACAS;MACAR;IACA;IAEA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAC;kBACA;oBACA;kBACA;oBACAV;sBACAC;sBACAC;oBACA;kBACA;gBACA;kBACAH;kBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAS;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;MACA;QACAd;QACA;MACA;IACA;IAEA;IACAe;MACA;MACA;MACA;MAEA;QACA;MACA;MAEA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAAC;MACA;IACA;IAEA;IACAC;MAAA;MACAlB;QACAE;QACAiB;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFAC;gBAGA;kBACAzB;oBACAE;oBACAD;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAF;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA1B;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAyB;gBACAC;gBAAA;gBAGAC;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAhD;kBACAG;kBACAC;kBACAC;kBACA4C;kBACAC;gBACA;cAAA;gBAPAN;gBASA;kBACAzB;oBACAE;oBACAD;kBACA;kBACAE;oBACA;kBACA;gBACA;kBACAH;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAF;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7bA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/chajia.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/chajia.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./chajia.vue?vue&type=template&id=7ae43980&scoped=true&\"\nvar renderjs\nimport script from \"./chajia.vue?vue&type=script&lang=js&\"\nexport * from \"./chajia.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chajia.vue?vue&type=style&index=0&id=7ae43980&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7ae43980\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/chajia.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=template&id=7ae43980&scoped=true&\"", "var components\ntry {\n  components = {\n    \"u-Form\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--form/u--form\" */ \"uview-ui/components/u--form/u--form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-form-item/u-form-item\" */ \"uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--textarea/u--textarea\" */ \"uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.orderInfo ? _vm.getOrderStatusText(_vm.orderInfo.payType) : null\n  var m1 =\n    _vm.diffApplyForm.diffAmount && parseFloat(_vm.diffApplyForm.diffAmount) > 0\n  var m2 = m1 ? _vm.formatAmount(_vm.diffApplyForm.diffAmount) : null\n  var m3 = m1 ? _vm.formatAmount(_vm.serviceFee) : null\n  var m4 = m1 ? _vm.formatAmount(_vm.actualAmount) : null\n  var g0 =\n    _vm.orderInfo &&\n    _vm.orderInfo.orderDiffPriceList &&\n    _vm.orderInfo.orderDiffPriceList.length > 0\n  var m5 = g0\n    ? _vm.getScrollViewHeight(_vm.orderInfo.orderDiffPriceList.length)\n    : null\n  var l0 = g0\n    ? _vm.__map(\n        _vm.orderInfo.orderDiffPriceList,\n        function (diffItem, diffIndex) {\n          var $orig = _vm.__get_orig(diffItem)\n          var m6 = _vm.getDiffStatusText(diffItem.status)\n          var m7 = _vm.formatWarrantyDate(diffItem.partsWarrantyPeriod)\n          var m8 = diffItem.partsWarrantyPeriod\n            ? _vm.getRemainingWarrantyDays(diffItem.partsWarrantyPeriod)\n            : null\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n            m8: m8,\n          }\n        }\n      )\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        g0: g0,\n        m5: m5,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\n\n\t\t<view class=\"content\">\n\t\t\t<view class=\"order-info\" v-if=\"orderInfo\">\n\t\t\t\t<view class=\"order-header\">\n\t\t\t\t\t<view class=\"order-no\">订单号：{{ orderInfo.orderCode }}</view>\n\t\t\t\t\t<view class=\"order-status\">{{ getOrderStatusText(orderInfo.payType) }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-detail\">\n\t\t\t\t\t<view class=\"goods-info\">\n\t\t\t\t\t\t<image :src=\"orderInfo.goodsCover\" class=\"goods-image\"></image>\n\t\t\t\t\t\t<view class=\"goods-text\">\n\t\t\t\t\t\t\t<text class=\"goods-name\">{{ orderInfo.goodsName }}</text>\n\t\t\t\t\t\t\t<text class=\"goods-price\">￥{{ orderInfo.coachServicePrice }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-container\">\n\t\t\t\t<view class=\"form-title\">申请差价</view>\n\t\t\t\t<u--form labelPosition=\"left\" :model=\"diffApplyForm\" :rules=\"diffApplyRules\" ref=\"diffApplyForm\">\n\t\t\t\t\t<u-form-item label=\"差价金额\" prop=\"diffAmount\" ref=\"item1\">\n\t\t\t\t\t\t<u--input v-model=\"diffApplyForm.diffAmount\" placeholder=\"请输入差价金额\" type=\"number\"\n\t\t\t\t\t\t\tborder=\"none\" @input=\"onDiffAmountChange\"></u--input>\n\t\t\t\t\t</u-form-item>\n\n\t\t\t\t\t<!-- 费用明细显示区域 -->\n\t\t\t\t\t<transition name=\"fade-slide\">\n\t\t\t\t\t\t<view class=\"fee-detail-container\" v-if=\"diffApplyForm.diffAmount && parseFloat(diffApplyForm.diffAmount) > 0\">\n\t\t\t\t\t\t\t<view class=\"fee-detail-title\">费用明细</view>\n\t\t\t\t\t\t\t<view class=\"fee-detail-content\">\n\t\t\t\t\t\t\t\t<view class=\"fee-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"fee-label\">差价金额</view>\n\t\t\t\t\t\t\t\t\t<view class=\"fee-value primary\">¥{{ formatAmount(diffApplyForm.diffAmount) }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"fee-item\">\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"fee-label\">服务费 ({{ commissionRate }}%)</view> -->\n\t\t\t\t\t\t\t\t\t<view class=\"fee-label\">服务费</view>\n\t\t\t\t\t\t\t\t\t<view class=\"fee-value danger\">-¥{{ formatAmount(serviceFee) }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"fee-divider\"></view>\n\t\t\t\t\t\t\t\t<view class=\"fee-item total\">\n\t\t\t\t\t\t\t\t\t<view class=\"fee-label\">预估到手金额</view>\n\t\t\t\t\t\t\t\t\t<view class=\"fee-value success\">¥{{ formatAmount(actualAmount) }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</transition>\n\n\t\t\t\t\t<u-form-item label=\"差价原因\">\n\t\t\t\t\t\t<view class=\"reason-type-display\">\n\t\t\t\t\t\t\t<text class=\"reason-type-text\">配件不符合</text>\n\t\t\t\t\t\t\t<view class=\"reason-type-badge\">类型: 1</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</u-form-item>\n\t\t\t\t\t<u-form-item label=\"原因详情\" prop=\"reasonDetail\" ref=\"item3\">\n\t\t\t\t\t\t<u--textarea v-model=\"diffApplyForm.reasonDetail\" placeholder=\"请输入差价原因详情\"\n\t\t\t\t\t\t\tcount></u--textarea>\n\t\t\t\t\t</u-form-item>\n\t\t\t\t\t<u-form-item label=\"配件质保时长\" prop=\"partsWarrantyDuration\" ref=\"item4\">\n\t\t\t\t\t\t<view class=\"warranty-input-group\">\n\t\t\t\t\t\t\t<u--input v-model=\"diffApplyForm.partsWarrantyDuration\" placeholder=\"请输入质保天数\"\n\t\t\t\t\t\t\t\ttype=\"number\" border=\"none\"></u--input>\n\t\t\t\t\t\t\t<text class=\"unit-text\">天</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</u-form-item>\n\t\t\t\t\t<u-form-item label=\"配件图\" class=\"last-form-item\" ref=\"item5\">\n\t\t\t\t\t\t<view class=\"upload-container\">\n\t\t\t\t\t\t\t<upload @upload=\"imgUploadDiff\" @del=\"imgUploadDiff\"\n\t\t\t\t\t\t\t\t:imagelist=\"diffApplyForm.partsimgs\" imgtype=\"partsimgs\" imgclass=\"parts-img\"\n\t\t\t\t\t\t\t\ttext=\"上传配件图\" :imgsize=\"9\"></upload>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</u-form-item>\n\t\t\t\t</u--form>\n\t\t\t</view>\n\n\n\n\t\t\t<view v-if=\"orderInfo && orderInfo.orderDiffPriceList && orderInfo.orderDiffPriceList.length > 0\" class=\"sub_orders\">\n\t\t\t\t<view class=\"sub_title\">差价申请记录</view>\n\t\t\t\t<scroll-view class=\"sub_scroll_container\" scroll-y=\"true\" :style=\"{height: getScrollViewHeight(orderInfo.orderDiffPriceList.length) + 'rpx'}\">\n\t\t\t\t\t<view class=\"sub_item\" v-for=\"(diffItem, diffIndex) in orderInfo.orderDiffPriceList\" :key=\"diffItem.id\">\n\t\t\t\t\t\t<view class=\"sub_head\">\n\t\t\t\t\t\t\t<view class=\"sub_no\">差价单号：{{ diffItem.diffCode }}</view>\n\t\t\t\t\t\t\t<view class=\"sub_status\">{{ getDiffStatusText(diffItem.status) }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"sub_content\">\n\t\t\t\t\t\t\t<view class=\"sub_info_grid\">\n\t\t\t\t\t\t\t\t<view class=\"sub_info_row\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub_info_item\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub_label\">差价金额：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub_value sub_amount_value\">￥{{ diffItem.diffAmount }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub_info_item sub-warranty-info\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub-label\">质保日期：</text>\n\t\t\t\t\t\t\t\t\t\t<view class=\"sub-warranty-details\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub-value sub-warranty-value\">{{ formatWarrantyDate(diffItem.partsWarrantyPeriod) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"remaining-days-badge\" v-if=\"diffItem.partsWarrantyPeriod\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text>{{ getRemainingWarrantyDays(diffItem.partsWarrantyPeriod) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"sub_info_row\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub_info_item sub_reason_item\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub_label\">原因：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub_value sub_reason_value\">{{ diffItem.reasonDetail }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"sub_info_row\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub_info_item\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub_label\">申请时间：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub_value sub_time_value\">{{ diffItem.createdTime }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"sub_actions\">\n\t\t\t\t\t\t\t\t<view class=\"sub_qzf\" v-if=\"diffItem.status === 0\"\n\t\t\t\t\t\t\t\t\************=\"showDiffCancelModal(diffItem)\">\n\t\t\t\t\t\t\t\t\t取消差价\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"btn-cancel\" @click=\"goBack\">取消</view>\n\t\t\t<view class=\"btn-confirm\" @click=\"diffApplyConfirm\">提交申请</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport Upload from '@/components/upload.vue'; // Import upload component\n\nexport default {\n\tcomponents: {\n\t\tUpload,\n\t},\n\tdata() {\n\t\treturn {\n\t\t\torderInfo: null, // 订单信息\n\t\t\torderId: '', // 订单ID\n\t\t\tcommissionRate: 0, // 服务费比例\n\t\t\tdiffApplyForm: {\n\t\t\t\tdiffAmount: '',\n\t\t\t\treasonType: 1, // 差价原因类型，目前固定为1代表配件不符合\n\t\t\t\treasonDetail: '',\n\t\t\t\tpartsWarrantyDuration: '', // 新增：配件质保时长（天）\n\t\t\t\tpartsimgs: [], // 配件图片\n\t\t\t},\n\t\t\tdiffApplyRules: {\n\t\t\t\tdiffAmount: [{\n\t\t\t\t\trequired: true,\n\t\t\t\t\tmessage: '请输入差价金额',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}, {\n\t\t\t\t\tvalidator: (rule, value, callback) => {\n\t\t\t\t\t\treturn value >= 0.01;\n\t\t\t\t\t},\n\t\t\t\t\tmessage: '差价金额必须大于等于0.01',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}],\n\t\t\t\treasonDetail: [{\n\t\t\t\t\trequired: true,\n\t\t\t\t\tmessage: '请输入差价原因详情',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}],\n\t\t\t\tpartsWarrantyDuration: [{\n\t\t\t\t\trequired: true,\n\t\t\t\t\tmessage: '请输入质保天数',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}, {\n\t\t\t\t\tvalidator: (rule, value, callback) => {\n\t\t\t\t\t\treturn value > 0;\n\t\t\t\t\t},\n\t\t\t\t\tmessage: '质保天数必须大于0',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}],\n\t\t\t},\n\t\t\tpay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 计算服务费\n\t\tserviceFee() {\n\t\t\tconst diffAmount = parseFloat(this.diffApplyForm.diffAmount) || 0;\n\t\t\treturn (diffAmount * this.commissionRate / 100).toFixed(2);\n\t\t},\n\t\t// 计算实际到手金额\n\t\tactualAmount() {\n\t\t\tconst diffAmount = parseFloat(this.diffApplyForm.diffAmount) || 0;\n\t\t\tconst serviceFee = parseFloat(this.serviceFee) || 0;\n\t\t\treturn (diffAmount - serviceFee).toFixed(2);\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 获取服务费比例\n\t\tthis.$api.shifu.getpercentageOfCommission().then(res=>{\n\t\t\tconsole.log(res)\n\t\t\tif (res.code === \"200\") {\n\t\t\t\tthis.commissionRate = res.data || 0;\n\t\t\t}\n\t\t}).catch(err => {\n\t\t\tconsole.error('获取服务费比例失败:', err);\n\t\t});\n\n\t\tif (options.orderId) {\n\t\t\tthis.orderId = options.orderId;\n\t\t\tthis.loadOrderInfo();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ticon: 'none',\n\t\t\t\ttitle: '订单信息缺失'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.goBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\tonReady() {\n\t\tthis.$nextTick(() => {\n\t\t\tif (this.$refs.diffApplyForm && this.$refs.diffApplyForm.setRules) {\n\t\t\t\tthis.$refs.diffApplyForm.setRules(this.diffApplyRules);\n\t\t\t}\n\t\t});\n\t},\n\tmethods: {\n\t\t// 格式化金额显示\n\t\tformatAmount(amount) {\n\t\t\tconst num = parseFloat(amount) || 0;\n\t\t\treturn num.toFixed(2);\n\t\t},\n\n\t\t// 差价金额输入变化处理\n\t\tonDiffAmountChange(value) {\n\t\t\t// 可以在这里添加实时验证逻辑\n\t\t\tconsole.log('差价金额变化:', value);\n\t\t},\n\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\n\t\t// 加载订单信息\n\t\tasync loadOrderInfo() {\n\t\t\ttry {\n\t\t\t\tconst orderDetails = uni.getStorageSync('orderdetails');\n\t\t\t\tif (orderDetails && orderDetails.id == this.orderId) {\n\t\t\t\t\tthis.orderInfo = orderDetails;\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '订单信息获取失败'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载订单信息失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '订单信息获取失败'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 获取订单状态文本\n\t\tgetOrderStatusText(payType) {\n\t\t\treturn this.pay_typeArr[payType] || '未知状态';\n\t\t},\n\n\t\t// 获取差价申请状态文本\n\t\tgetDiffStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'-1': '已取消',\n\t\t\t\t0: '待确认',\n\t\t\t\t1: '已确认待支付',\n\t\t\t\t2: '已支付',\n\t\t\t\t3: '已拒绝'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知状态';\n\t\t},\n\n\t\t// 格式化配件质保日期\n\t\tformatWarrantyDate(timestamp) {\n\t\t\tif (!timestamp) {\n\t\t\t\treturn '无质保信息';\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tconst date = new Date(timestamp);\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\treturn '无效日期';\n\t\t\t\t}\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('格式化质保日期失败:', error);\n\t\t\t\treturn '格式错误';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取剩余质保天数\n\t\tgetRemainingWarrantyDays(timestamp) {\n\t\t\tif (!timestamp) return '';\n\t\t\tconst now = new Date();\n\t\t\tconst warrantyDate = new Date(timestamp);\n\t\t\t\n\t\t\tif (isNaN(warrantyDate.getTime()) || warrantyDate < now) {\n\t\t\t\treturn '已过期';\n\t\t\t}\n\t\t\t\n\t\t\tconst diffTime = warrantyDate.getTime() - now.getTime();\n\t\t\tconst diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\t\t\t\n\t\t\treturn `剩余${diffDays}天`;\n\t\t},\n\n\t\t// 获取滚动视图高度\n\t\tgetScrollViewHeight(itemCount) {\n\t\t\tconst maxHeight = 450;\n\t\t\tconst itemHeight = 150;\n\t\t\tconst calculatedHeight = Math.min(itemCount * itemHeight, maxHeight);\n\t\t\treturn calculatedHeight;\n\t\t},\n\n\t\t// 处理差价申请中的图片上传\n\t\timgUploadDiff(e) {\n\t\t\tconst { imagelist, imgtype } = e;\n\t\t\tthis.$set(this.diffApplyForm, imgtype, imagelist);\n\t\t},\n\n\t\t// 显示取消差价申请确认弹窗\n\t\tshowDiffCancelModal(diffItem) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '取消差价申请',\n\t\t\t\tcontent: `确定要取消差价单号 ${diffItem.diffCode} 的申请吗？`,\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.diffCancel(diffItem);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 取消差价申请\n\t\tasync diffCancel(diffItem) {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.diffCancel({\n\t\t\t\t\tid: diffItem.id\n\t\t\t\t});\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '差价申请已取消',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.loadOrderInfo();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '取消失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error in diffCancel:', err);\n\t\t\t}\n\t\t},\n\n\t\t// 提交差价申请\n\t\tasync diffApplyConfirm() {\n\t\t\ttry {\n\t\t\t\tif (!this.$refs.diffApplyForm || !this.$refs.diffApplyForm.validate) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '表单未准备就绪，请稍后重试'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tawait this.$refs.diffApplyForm.validate();\n\n\t\t\t\tif (!this.orderInfo) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '订单信息缺失'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst partsWarrantyPeriodTimestamp = now.getTime() + (this.diffApplyForm.partsWarrantyDuration * 24 * 60 * 60 * 1000);\n\n\t\t\t\ttry {\n\t\t\t\t\tconst partsImgsString = this.diffApplyForm.partsimgs.map(img => img.path).join(',');\n\n\t\t\t\t\tconst res = await this.$api.shifu.diffApply({\n\t\t\t\t\t\torderId: this.orderInfo.id,\n\t\t\t\t\t\tdiffAmount: parseFloat(this.diffApplyForm.diffAmount),\n\t\t\t\t\t\treasonType: this.diffApplyForm.reasonType,\n\t\t\t\t\t\treasonDetail: this.diffApplyForm.reasonDetail,\n\t\t\t\t\t\tpartsImgs: partsImgsString,\n\t\t\t\t\t\tpartsWarrantyPeriod: partsWarrantyPeriodTimestamp\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '差价申请成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.goBack();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '差价申请失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Error in diffApply:', err);\n\t\t\t\t}\n\t\t\t} catch (errors) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请检查填写信息'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground-color: #F8F8F8;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n.header {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tz-index: 100;\n\theight: 88rpx;\n\tbackground: #FFFFFF;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\n\t.back-btn {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\t.back-icon {\n\t\t\tfont-size: 36rpx;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n\n\t.title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t}\n\n\t.placeholder {\n\t\twidth: 60rpx;\n\t}\n}\n\n.content {\n\n\tpadding: 88rpx 30rpx 20rpx;\n}\n\n.order-info {\n\tbackground: #FFFFFF;\n\tborder-radius: 24rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\n\t.order-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\n\t\t.order-no {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #666;\n\t\t}\n\n\t\t.order-status {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #2E80FE;\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n\n\t.order-detail {\n\t\t.goods-info {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.goods-image {\n\t\t\t\twidth: 120rpx;\n\t\t\t\theight: 120rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\n\t\t\t.goods-text {\n\t\t\t\tflex: 1;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\n\t\t\t\t.goods-name {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t}\n\n\t\t\t\t.goods-price {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #ff6b35;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.form-container {\n\tbackground: #FFFFFF;\n\tborder-radius: 24rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\n\t.form-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tmargin-bottom: 30rpx;\n\t}\n}\n\n// 费用明细样式\n.fee-detail-container {\n\tmargin-top: 30rpx;\n\tpadding: 24rpx;\n\tbackground: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);\n\tborder-radius: 16rpx;\n\tborder: 1rpx solid #e8f0ff;\n\tbox-shadow: 0 4rpx 20rpx rgba(46, 128, 254, 0.08);\n\tposition: relative;\n\toverflow: hidden;\n\n\t&::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 4rpx;\n\t\tbackground: linear-gradient(90deg, #2E80FE 0%, #1a6fd1 50%, #2E80FE 100%);\n\t}\n\n\t.fee-detail-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\twidth: 6rpx;\n\t\t\theight: 20rpx;\n\t\t\tbackground: linear-gradient(135deg, #2E80FE 0%, #1a6fd1 100%);\n\t\t\tborder-radius: 3rpx;\n\t\t\tmargin-right: 12rpx;\n\t\t}\n\t}\n\n\t.fee-detail-content {\n\t\t.fee-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 16rpx 0;\n\t\t\ttransition: all 0.3s ease;\n\t\t\tborder-radius: 8rpx;\n\n\t\t\t&:hover {\n\t\t\t\tbackground: rgba(46, 128, 254, 0.05);\n\t\t\t\ttransform: translateX(4rpx);\n\t\t\t}\n\n\t\t\t&.total {\n\t\t\t\tpadding: 20rpx 16rpx 16rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tbackground: linear-gradient(135deg, rgba(0, 178, 106, 0.1) 0%, rgba(0, 178, 106, 0.05) 100%);\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tmargin: 8rpx -8rpx 0;\n\t\t\t\tposition: relative;\n\n\t\t\t\t&::before {\n\t\t\t\t\tcontent: '';\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\ttop: 50%;\n\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\twidth: 4rpx;\n\t\t\t\t\theight: 60%;\n\t\t\t\t\tbackground: linear-gradient(135deg, #00b26a 0%, #00a85a 100%);\n\t\t\t\t\tborder-radius: 2rpx;\n\t\t\t\t}\n\n\t\t\t\t.fee-label {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t}\n\n\t\t\t\t.fee-value {\n\t\t\t\t\tfont-size: 34rpx;\n\t\t\t\t\tfont-weight: 700;\n\t\t\t\t\ttext-shadow: 0 1rpx 2rpx rgba(0, 178, 106, 0.2);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.fee-label {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tflex: 1;\n\t\t\t}\n\n\t\t\t.fee-value {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\ttext-align: right;\n\n\t\t\t\t&.primary {\n\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t}\n\n\t\t\t\t&.danger {\n\t\t\t\t\tcolor: #ff6b35;\n\t\t\t\t}\n\n\t\t\t\t&.success {\n\t\t\t\t\tcolor: #00b26a;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.fee-divider {\n\t\t\theight: 2rpx;\n\t\t\tbackground: linear-gradient(90deg, transparent 0%, rgba(46, 128, 254, 0.3) 20%, rgba(46, 128, 254, 0.6) 50%, rgba(46, 128, 254, 0.3) 80%, transparent 100%);\n\t\t\tmargin: 20rpx 0;\n\t\t\tborder-radius: 1rpx;\n\t\t\tposition: relative;\n\n\t\t\t&::after {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 50%;\n\t\t\t\tleft: 50%;\n\t\t\t\ttransform: translate(-50%, -50%);\n\t\t\t\twidth: 8rpx;\n\t\t\t\theight: 8rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tbox-shadow: 0 0 8rpx rgba(46, 128, 254, 0.4);\n\t\t\t}\n\t\t}\n\t}\n}\n\n.warranty-input-group {\n\tdisplay: flex;\n\talign-items: center;\n\t\n\t.unit-text {\n\t\tmargin-left: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n}\n\n.footer {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: #FFFFFF;\n\tpadding: 20rpx 30rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n\tdisplay: flex;\n\tgap: 20rpx;\n\n\t.btn-cancel,\n\t.btn-confirm {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t\ttransition: all 0.2s;\n\t}\n\n\t.btn-cancel {\n\t\tbackground: #f8f8f8;\n\t\tcolor: #666;\n\t}\n\t\n\t.btn-confirm {\n\t\tbackground: #2E80FE;\n\t\tcolor: #fff;\n\t}\n\n\t.btn-cancel:active {\n\t\tbackground: #e8e8e8;\n\t}\n\n\t.btn-confirm:active {\n\t\tbackground: #1a6fd1;\n\t}\n}\n\n// 差价申请记录样式\n.sub_orders {\n\tbackground: #FFFFFF;\n\tborder-radius: 24rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\n\t.sub_title {\n\t\tfont-size: 26rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #666;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.sub_scroll_container {\n\t\tmax-height: 450rpx;\n\t\toverflow-y: auto;\n\t}\n\n\t.sub_item {\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 20rpx;\n\t\tmargin-bottom: 15rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\n\t\t.sub_head {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 15rpx;\n\n\t\t\t.sub_no {\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tmax-width: 400rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\n\t\t\t.sub_status {\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\t\t}\n\n\t\t.sub_content {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: flex-start;\n\n\t\t\t.sub_info_grid {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-right: 20rpx;\n\n\t\t\t\t.sub_info_row {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t\tgap: 20rpx;\n\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t.sub_info_item {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\tmin-width: 0;\n\n\t\t\t\t\t\t&.sub_reason_item {\n\t\t\t\t\t\t\tflex: 2;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sub_label {\n\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\tmargin-right: 8rpx;\n\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sub_value {\n\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\tmin-width: 0;\n\n\t\t\t\t\t\t\t&.sub_amount_value {\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\tcolor: #ff6b35;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&.sub_warranty-value {\n\t\t\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&.sub_reason_value {\n\t\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&.sub_time_value {\n\t\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sub_actions {\n\t\t\t\tflex-shrink: 0;\n\t\t\t\talign-self: flex-start;\n\n\t\t\t\t.sub_qzf {\n\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tbackground: #ff6b6b;\n\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\tfont-size: 18rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tline-height: 40rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 表单样式\n.upload-container {\n\tmargin-top: 16rpx;\n\tpadding: 20rpx;\n\tborder: 1rpx dashed #ccc;\n\tborder-radius: 8rpx;\n\tbackground: #fafafa;\n}\n\n.parts-img {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tmargin-right: 12rpx;\n\tmargin-bottom: 12rpx;\n\tborder-radius: 8rpx;\n}\n\n.reason-type-display {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 16rpx;\n\tbackground: #f8f8f8;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 8rpx;\n}\n\n.reason-type-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.reason-type-badge {\n\tbackground: #2E80FE;\n\tcolor: #fff;\n\tfont-size: 20rpx;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 12rpx;\n}\n\n// 表单项样式优化\n/deep/ .u-form-item {\n\tmargin-bottom: 24rpx;\n\tpadding-bottom: 20rpx;\n}\n\n/deep/ .u-form-item:not(.last-form-item) {\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n/deep/ .u-form-item:last-child {\n\tmargin-bottom: 0;\n}\n\n/deep/ .u-form-item__body {\n\tpadding-left: 0 !important;\n}\n\n/deep/ .u-form-item__label {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tmargin-bottom: 16rpx;\n\tfont-weight: 600;\n\tposition: relative;\n\n\t&::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tbottom: -8rpx;\n\t\tleft: 0;\n\t\twidth: 30rpx;\n\t\theight: 3rpx;\n\t\tbackground: linear-gradient(90deg, #2E80FE 0%, #1a6fd1 100%);\n\t\tborder-radius: 2rpx;\n\t}\n}\n\n/deep/ .u--input__content {\n\tbackground: #f8f9ff;\n\tborder: 1rpx solid #e8f0ff;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx;\n\tfont-size: 30rpx;\n\tcolor: #333;\n\ttransition: all 0.3s ease;\n\n\t&:focus {\n\t\tbackground: #ffffff;\n\t\tborder-color: #2E80FE;\n\t\tbox-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);\n\t}\n}\n\n/deep/ .u--textarea__content {\n\tbackground: #f8f8f8;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 8rpx;\n\tpadding: 16rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmin-height: 120rpx;\n}\n\n// 动画效果\n.fade-slide-enter-active,\n.fade-slide-leave-active {\n\ttransition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n}\n\n.fade-slide-enter-from {\n\topacity: 0;\n\ttransform: translateY(-20rpx);\n}\n\n.fade-slide-leave-to {\n\topacity: 0;\n\ttransform: translateY(-10rpx);\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=style&index=0&id=7ae43980&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=style&index=0&id=7ae43980&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755734933750\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}